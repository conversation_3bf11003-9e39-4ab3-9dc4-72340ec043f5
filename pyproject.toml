# Python project configuration
# Tools fully compatible with pyproject.toml:
# ✅ Black - Code formatter
# ✅ isort - Import sorter
# ✅ pytest - Testing framework
# ✅ mypy - Type checker
# ✅ bandit - Security linter
# ✅ coverage.py - Test coverage
# ✅ ruff - Modern linter (can replace Flake8)
# ✅ build - Build system
# ✅ poetry - Dependency management
# ✅ setuptools - Package management
#
# Tools NOT fully compatible (need their own config files):
# ❌ Pylint - Needs .pylintrc
# ❌ Flake8 - Can use pyproject.toml but plugins might have issues
# ❌ autopep8 - Needs setup.cfg or .autopep8

[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pacman-agent"
version = "0.1.0"
description = "Pacman Agent Project"
requires-python = ">=3.8"

[tool.black]
line-length = 120
target-version = ['py38']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-ra -q"

[tool.coverage.run]
source = ["src"]
omit = ["tests/*", "setup.py"]

[tool.ruff]
# Modern alternative to Flake8
line-length = 100
target-version = "py38"
select = ["E", "F", "B", "I"]  # Error, Flake8, Bugbear, isort
ignore = ["E501"]  # Ignore line too long

[tool.ruff.isort]
known-first-party = ["pacman_agent"]