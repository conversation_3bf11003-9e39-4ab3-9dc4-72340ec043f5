"""Funções utilitárias para automação do navegador."""

import os
import random
import time


def get_env_int(var_name: str, default_value: int) -> int:
    """Obtém uma variável de ambiente como inteiro com valor padrão."""
    try:
        env_value = os.getenv(var_name)
        if env_value is None:
            print(f"AVISO: Variável de ambiente {var_name} não definida. Usando valor padrão: {default_value}")
            return default_value

        # Remove espaços e comentários (tudo após #)
        clean_value = env_value.split("#")[0].strip()

        if not clean_value:
            print(f"AVISO: Variável de ambiente {var_name} está vazia. Usando valor padrão: {default_value}")
            return default_value

        parsed_value = int(clean_value)

        if parsed_value <= 0:
            print(
                f"AVISO: Variável de ambiente {var_name} tem valor inválido ({parsed_value}). Usando valor padrão: {default_value}"
            )
            return default_value

        return parsed_value

    except ValueError as e:
        print(
            f"ERRO: Não foi possível converter {var_name}='{env_value}' para inteiro. Usando valor padrão: {default_value}"
        )
        print(f"Detalhes do erro: {e}")
        return default_value


# Tempo de espera para navegação (padrão: 60 segundos)
NAVIGATE_WAIT_TIME: int = get_env_int("NAVIGATE_WAIT_TIME", 60)

# Tempo de espera para elementos aparecerem (padrão: 30 segundos)
ELEMENT_APPEAR_WAIT_TIME: int = get_env_int("ELEMENT_APPEAR_WAIT_TIME", 30)

print(f"Configurações carregadas:")
print(f"  - NAVIGATE_WAIT_TIME: {NAVIGATE_WAIT_TIME} segundos")
print(f"  - ELEMENT_APPEAR_WAIT_TIME: {ELEMENT_APPEAR_WAIT_TIME} segundos")


def random_delay(min_seconds: float = 1, max_seconds: float = 5) -> float:
    """Adiciona um delay aleatório para simular comportamento humano."""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)
    return delay
