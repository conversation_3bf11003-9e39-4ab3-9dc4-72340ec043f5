"""Funções utilitárias para automação do navegador."""

import os
import random
import time

# Tempo de espera para navegação
NAVIGATE_WAIT_TIME: int = int(os.getenv("NAVIGATE_WAIT_TIME") or "")
if NAVIGATE_WAIT_TIME <= 0:
    raise ValueError("NAVIGATE_WAIT_TIME environment variable is not defined or invalid")

ELEMENT_APPEAR_WAIT_TIME: int = int(os.getenv("ELEMENT_APPEAR_WAIT_TIME") or "")
if ELEMENT_APPEAR_WAIT_TIME <= 0:
    raise ValueError("ELEMENT_APPEAR_WAIT_TIME environment variable is not defined or invalid")


def random_delay(min_seconds: float = 1, max_seconds: float = 5) -> float:
    """Adiciona um delay aleatório para simular comportamento humano."""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)
    return delay
