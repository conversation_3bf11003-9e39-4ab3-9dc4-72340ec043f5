# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.pyc

# Virtualenv
.venv/
venv/
ENV/

# Environment files
.env

# Playwright
playwright-report/

# VSCode
.vscode/

# Mac/Linux
.DS_Store

# Ignore screenshots folder
screenshots/

# Ignore downloads folder
downloads/

# Python specific ignores
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Selenium specific
geckodriver.log