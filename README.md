# CLI

```sh
# Ativar ambiente virtual e executar o script
source venv/bin/activate

python -m main --cnpj 17.784.050/0001-00 --ano 2025 --mes 5 --firefox-profile=/home/<USER>/.mozilla/firefox/7l8cge4c.AVCP

deactivate # opcional ao terminar


# Olhe o diretório `downloads/17784050000100` neste mesmo projeto

# ./17784050000100-202505.csv
# ./17784050000100-202505.json
# ./17784050000100-202505.xlsx
# ./emprestimos-17784050000100-202505-20250515_155011.pdf

```

# Documentação

- Este `readme` que vos fala.
- [code-quality-tools.md](code-quality-tools.md)
- [layout-arquivo-emprestimos.md](layout-arquivo-emprestimos.md)
- [visual-studio-code.md](visual-studio-code.md)
- [cenarios.md](cenarios.md)

# Algoritmo Heurístico

A informação que precisamos coletar no site https://servicos.mte.gov.br/ se resume entre 1 à 3 etapas com os seguintes cenários:

## Pior cenário, pois é mais longo.

Cenário 1:

- Acessar tela de login
- Escolher a empresa (são mais 3 sub-passos)
- Finalmente a última etapa, a tela de consulta e download.

## Ótimo cenário, já estamos autenticados

Cenário 2:

- Acessar diretamente a última etapa através do link que já conhecemos a URL.

## Conclusão

Esse robô é otimista, sempre tentará ir pela etapa mais rápida, que no nosso caso é o cenário 2.

# Regras de negócio

- [x] Tirar `screenshot` ao dar um erro

# Perfil do Firefox

## Linux

**Abrir o firefox com uma modal onde se pode gerenciar os perfis**

```sh
firefox --no-remote -P
```

Abrir o Firefox utilizando um perfil previamente criado:

```sh
firefox --no-remote -P meu-perfil-personalizado
```

## Mac

# Trecho HTML

Abaixo está o código HTML que é quase impossível de vê-lo porque o popup desaparece ao tentar inspecioná-lo.

HTML do popup que aparece ao clicar na seta no avatar do usuário.
É impossível ver o HTML abaixo via inspecionar elemento de forma normal.

```html
<div class="_menu_1cso3_1">
<div class="d-flex flex-column gap-3">
    <div>
        <div><span class="label text-base text-semi-bold">Usuário login</span></div>
        <div><span class="text-break text-wrap text-up-01 text-medium">ANTONIA MARIA BORGES</span></div>
    </div>
    <span class="br-divider"></span>
    <div>
        <div><span class="label text-base text-semi-bold">CNPJ da empresa</span></div>
        <div><span class="text-break text-wrap text-up-01 text-medium"><span inputmode="numeric">17.784.050/0001-00</span></span></div>
    </div>
    <div>
        <div><span class="label text-base text-semi-bold">Razão social da empresa</span></div>
        <div><span class="text-break text-wrap text-up-01 text-medium">AVCP COMERCIAL DE PRODUTOS E SERVICOS LTDA</span></div>
    </div>
    <span class="br-divider"></span>
</div>
<button class="br-button mt-4 primary" type="button">Trocar empresa</button><button class="br-button mt-2 secondary" type="button">Sair da conta</button>
</div>
```

# Selenium

É necessário utilizar recursos do Python como `time.sleep(10)` para que a navegação não seja muito rápida assim causando o disparo de `captcha`. O valor certo para que isso não aconteça ainda é um palpite.

Navegações instantâneas, ou seja, que não fazem um HTTP request não sofrem desse mal.

## Técnicas Anti-Detecção

Sites modernos podem detectar quando um navegador está sendo controlado por automação (Selenium/WebDriver). Para contornar isso, implementamos as seguintes técnicas:

```python
# Configurações anti-detecção
options.set_preference("dom.webdriver.enabled", False)     # Remove flag navigator.webdriver
options.set_preference("useAutomationExtension", False)    # Desabilita extensão de automação
options.set_preference("marionette", False)                # Tenta ocultar protocolo Marionette
options.set_preference("general.useragent.override", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0")  # User-agent personalizado
options.set_preference("browser.privatebrowsing.autostart", False)  # Desabilita modo incognito (perfil normal é menos suspeito)
```

## Solução Avançada: Extensão Anti-Detecção Personalizada

Mesmo com todas as configurações acima, sites sofisticados ainda podem detectar a propriedade `navigator.webdriver=true`. Para contornar isso, implementamos uma extensão Firefox personalizada que ataca o problema em múltiplas camadas:

### Como a Detecção de Automação Funciona

Os sites detectam o Selenium WebDriver através de várias técnicas sofisticadas:

1. **Detecção de propriedades webdriver**: O navegador automatizado expõe a propriedade `navigator.webdriver=true`, que sites verificam instantaneamente.

2. **Fingerprinting do navegador**: Sites analisam características como:

   - Canvas fingerprinting (padrões de renderização)
   - WebGL fingerprinting (capacidades 3D)
   - Fontes disponíveis
   - Plugins e extensões (ou falta deles)
   - Hardware concurrency e memória
   - Plataforma e user-agent

3. **Inconsistências comportamentais**:

   - Movimentos muito precisos do mouse
   - Timing de eventos muito consistente
   - Falta de movimentos aleatórios

4. **Detecção de elementos específicos do Selenium**:

   - Atributos como `webdriver`, `selenium`, `driver` no documento
   - Propriedades ocultas como `document.$cdc_asdjflasutopfhvcZLmcfl_`
   - Elementos da shadow DOM do WebDriver

5. **Timing e padrões de rede**: Padrões de solicitação muito consistentes.

### Nossa Solução: Extensão Firefox Personalizada

Implementamos uma extensão que:

1. **Aplica múltiplas camadas de proteção à propriedade `navigator.webdriver`**:

   - Redefine a propriedade no objeto navigator
   - Esconde a propriedade no protótipo do navigator
   - Usa **defineGetter** para interceptar acessos
   - Aplica um Proxy para capturar qualquer tentativa de leitura

2. **Injeção de código priorizada**:

   - Injeta o código no início da página (document_start)
   - Executa antes do código de detecção do site
   - Usa injeção direta no DOM para máxima prioridade

3. **Remoção de atributos específicos**:

   - Remove atributos webdriver do documento
   - Elimina funções e objetos específicos do Selenium

4. **Forja plugins e fingerprints realistas**:
   - Simula plugins comuns do navegador
   - Modifica sutilmente canvas fingerprinting
   - Apresenta valores plausíveis para propriedades do hardware

```javascript
// Trecho da nossa extensão anti-detecção
(function() {
    'use strict';

    // NÍVEL 1: Redefinição principal de navigator.webdriver
    Object.defineProperty(navigator, 'webdriver', {
        get: function() { return false; },
        configurable: true
    });

    // NÍVEL 2: Esconder completamente a propriedade para que ela não apareça em Object.keys
    Object.defineProperty(Object.getPrototypeOf(navigator), 'webdriver', {
        get: function() { return false; },
        configurable: true
    });

    // NÍVEL 3: Sobrescrever com um getter vazio para evitar acesso direto
    window.Navigator.prototype.__defineGetter__('webdriver', function() { return false; });

    // NÍVEL 4: Criar um proxy em torno do objeto navigator para interceptar acessos
    const navigatorHandler = {
        get: function(target, prop) {
            if (prop === 'webdriver') {
                return false;
            }
            return target[prop];
        }
    };

    // Tentar criar um proxy para o navigator
    try {
        window.navigator = new Proxy(navigator, navigatorHandler);
    } catch (e) {
        // Ignorar erros
    }

    // Proteções adicionais de fingerprinting...
    // Código completo na implementação real
})();
```

### Implementação no Código Python

```python
# Criar e instalar a extensão anti-detecção
EXTENSIONS_DIR = "extensions"
EXTENSION_PATH = os.path.join(EXTENSIONS_DIR, "stealth_extension")
XPI_PATH = os.path.join(EXTENSIONS_DIR, "stealth_extension.xpi")

# Criar os arquivos necessários da extensão (manifest.json, stealth.js)
# ...código de criação de arquivos...

# Empacotar como XPI e instalar no Firefox
with zipfile.ZipFile(XPI_PATH, 'w') as xpi:
    xpi.write(os.path.join(EXTENSION_PATH, "manifest.json"), "manifest.json")
    xpi.write(os.path.join(EXTENSION_PATH, "stealth.js"), "stealth.js")

# Configurar e iniciar o driver com a extensão
options.set_preference("xpinstall.signatures.required", False)
driver = webdriver.Firefox(options=options, service=service)
driver.install_addon(XPI_PATH, temporary=True)
```

### Resultados e Verificação

A eficácia da nossa solução pode ser verificada executando este comando no console do navegador:

```javascript
console.log("webdriver detectado: " + (!!window.navigator.webdriver));
// Deve imprimir: "webdriver detectado: false"
```

Testes mostraram que esta abordagem evita a detecção na maioria dos sites, incluindo portais governamentais com medidas anti-automação. A extensão opera em um nível mais profundo do que simples configurações de preferências, interceptando e modificando as propriedades antes que o site possa detectá-las.

### Métodos de Detecção

Websites detectam automação através de:

- Propriedade `navigator.webdriver` (definida como true em navegadores automatizados)
- Propriedades e métodos específicos de janelas WebDriver
- Protocolo Marionette (usado pelo Firefox com Selenium)
- Padrões de comportamento não-humanos (timing muito regular)
- Inconsistências na fingerprint do navegador

### Recomendações Adicionais

Para melhorar a capacidade anti-detecção:

- Adicione delays aleatórios entre ações (`time.sleep(random.uniform(1, 3))`)
- Utilize um perfil Firefox limpo mas com histórico de navegação
- Execute ações com padrões humanos (movimentos não-lineares do mouse)
- Evite modo headless para sites com detecção avançada

### Sobre Navegação Privada/Incognito

O modo de navegação privada/incognito pode **aumentar** as chances de detecção e bloqueio:

- Navegadores em modo privado têm fingerprints mais genéricas, o que os torna mais fáceis de identificar como potencialmente automatizados
- Ausência de cookies, histórico e cache é um padrão comum em bots
- Sistemas anti-fraude consideram sessões de navegação privada como tendo maior risco
- Muitos sites de alto valor (bancos, governo) aplicam mais verificações a sessões incognito

Por isso, desativamos o modo incognito (`browser.privatebrowsing.autostart = False`) para usar um perfil Firefox normal com histórico existente, extensões comuns e cookies - o que parece mais "humano" para sistemas de detecção.

## Lidando com hCaptcha

Mesmo com todas as nossas configurações anti-detecção, o hCaptcha (`api.hcaptcha.com`) ainda pode detectar automação. Isso ocorre porque:

1. **Detecção avançada de comportamento**: O hCaptcha usa IA para analisar movimentos do mouse, timing de cliques e outras interações que são difíceis de simular de forma convincente.

2. **Canvas fingerprinting**: Captura detalhes únicos sobre como o navegador renderiza gráficos, que diferem entre navegação normal e automatizada.

3. **Análise de execução de JavaScript**: Detecta padrões na execução de JavaScript que revelam automação.

4. **Verificação de ambiente**: Identifica inconsistências no ambiente do navegador que as configurações básicas não conseguem ocultar.

### Soluções para o hCaptcha

Para lidar com serviços avançados como hCaptcha, considere:

1. **Serviços de resolução de CAPTCHA**: APIs como 2Captcha, Anti-Captcha ou CapMonster que utilizam humanos ou IA para resolver captchas.

2. **Bibliotecas especializadas**: Usar `undetected-chromedriver` ou `selenium-stealth` que implementam técnicas mais avançadas de evasão.

3. **Uso de proxy residencial rotativo**: IPs residenciais têm menor probabilidade de serem marcados como suspeitos.

4. **Perfil de navegador completo**: Use um perfil Firefox com extensões comuns, histórico, e cookies que simulam melhor um usuário real.

Exemplo de integração com serviço de resolução (pseudocódigo):

```python
# Quando encontrar hCaptcha
if "hcaptcha" in driver.page_source:
    # Capturar o site-key do hCaptcha
    site_key = driver.find_element_by_xpath('//div[@class="h-captcha"]').get_attribute('data-sitekey')

    # Enviar para serviço de resolução
    captcha_token = solver_service.solve_hcaptcha(site_key, driver.current_url)

    # Injetar token na página
    driver.execute_script(f'document.querySelector("[name=\'h-captcha-response\']").innerHTML="{captcha_token}";')

    # Continuar o fluxo
    submit_button.click()
```

### Quando o CAPTCHA Persiste Mesmo Após Resolução Manual

Um problema frustrante ocorre quando você resolve manualmente o CAPTCHA, mas o sistema continua insistindo que você é um robô. Isso acontece porque o hCaptcha não avalia apenas a resolução do desafio visual, mas também:

1. **Histórico de Contexto**: O hCaptcha examina seu histórico de navegação na sessão atual - se você chegou à página muito rapidamente ou seguiu um padrão de navegação não-humano, ele continuará suspeitando.

2. **Verificação em Camadas**: O sistema usa múltiplas camadas de verificação - mesmo resolvendo o quebra-cabeça visual, outros sinais técnicos do seu navegador controlado por Selenium podem continuar revelando automação.

3. **Tokens e Cookies**: O hCaptcha verifica tokens específicos e cookies que podem estar corrompidos ou ausentes em sessões automatizadas.

4. **Detecção Contínua**: A verificação é contínua, não apenas no momento da resolução do CAPTCHA.

#### Soluções para este problema:

1. **Usar um perfil de navegador real**: Em vez de iniciar um perfil novo a cada vez, utilize um perfil Firefox genuíno que você usa para navegação normal (com histório, cookies e extensões reais).

   ```python
   # Especifique o caminho para um perfil Firefox real e usado
   options.add_argument("-profile")
   options.add_argument("/home/<USER>/.mozilla/firefox/abc123.default")
   ```

2. **Simular comportamento de pré-navegação**: Antes de chegar à página com CAPTCHA, faça o navegador visitar algumas páginas relevantes para construir um histórico contextual:

   ```python
   # Simular uma jornada de navegação natural antes de chegar ao destino
   driver.get("https://www.google.com")
   time.sleep(random.uniform(2, 4))
   driver.get("https://www.google.com/search?q=portal+empregador+gov")
   time.sleep(random.uniform(3, 6))
   # Agora navegue para o site alvo
   driver.get(TARGET_URL)
   ```

3. **Limpar a sessão periodicamente**: Se você continua sendo bloqueado, tente limpar completamente a sessão, reiniciando o navegador e usando um novo IP:

   ```python
   # A cada N execuções, reinicie completamente o navegador
   if execution_count % 5 == 0:
       driver.quit()
       # Possivelmente troque o IP através de proxy
       driver = webdriver.Firefox(options=options)
   ```

4. **Utilizar uma extensão anti-fingerprinting**: Extensões como Privacy Badger ou Canvas Blocker podem ajudar a reduzir a eficácia da detecção de fingerprinting.

5. **Considerar o uso de bibliotecas específicas para Firefox**: Bibliotecas como `pyppeteer-stealth` ou adaptações do `selenium-stealth` para Firefox.

## Tratamento de Exceções no Selenium

Para detectar a ausência de elementos na página, use `TimeoutException` ao invés de `NoSuchElementException`. O `TimeoutException` é mais confiável pois:

1. É específico para cenários de espera
2. Respeita o tempo de espera configurado
3. Considera o carregamento dinâmico da página

`NoSuchElementException` pode ser enganoso porque:

1. Pode ser lançado muito rapidamente antes da página carregar completamente
2. Não respeita o tempo de espera que você configurou
3. Pode ser lançado por outros motivos além da ausência do elemento

Exemplo de uso correto:

```python
try:
    elemento = WebDriverWait(browser, TEMPO_ESPERA).until(
        EC.presence_of_element_located((By.XPATH, "xpath_do_elemento"))
    )
except TimeoutException:
    # Elemento não apareceu dentro do tempo de espera
    pass
```

Fontes:

- Documentação do Selenium WebDriver
- Código fonte dos bindings Python do Selenium

## Detecção de Downloads

Para detectar quando um download foi concluído, existem várias abordagens:

1. **Configuração do Firefox**:

```python
options.set_preference("browser.download.folderList", 2)  # Usa pasta personalizada
options.set_preference("browser.download.dir", os.path.abspath(DOWNLOADS_DIR))
options.set_preference("browser.download.useDownloadDir", True)
options.set_preference("browser.download.viewableInternally.enabledTypes", "")
options.set_preference("browser.helperApps.neverAsk.saveToDisk", "application/json,text/json")
```

2. **Monitoramento do Diretório**:

```python
def wait_for_download(download_dir: str, timeout: int = 30) -> str | None:
    start_time = time.time()
    initial_files = set(os.listdir(download_dir))

    while time.time() - start_time < timeout:
        current_files = set(os.listdir(download_dir))
        new_files = current_files - initial_files
        if new_files:
            # Espera um pouco para garantir que o arquivo foi completamente escrito
            time.sleep(1)
            return new_files.pop()
        time.sleep(0.5)
    return None
```

3. **Monitoramento de Indicadores de Progresso**:

```python
# Espera o indicador de progresso desaparecer
WebDriverWait(driver, timeout).until_not(
    EC.presence_of_element_located((By.CLASS_NAME, "download-progress"))
)
```

4. **Monitoramento de Requisições**:

```python
from selenium.webdriver.support.events import EventFiringWebDriver, AbstractEventListener

class DownloadListener(AbstractEventListener):
    def __init__(self):
        self.downloads = []

    def after_navigate_to(self, url, driver):
        # Verifica se a resposta é um download
        if "Content-Disposition" in driver.get_log("performance"):
            self.downloads.append(url)

driver = EventFiringWebDriver(webdriver.Firefox(), DownloadListener())
```

A abordagem mais confiável é combinar #1 e #2 - configurar o Firefox para usar um diretório específico e monitorar esse diretório por mudanças. Isso é mais robusto que usar um tempo fixo de espera.

# Erros conhecidos

> `WebDriverException`: Message: Process unexpectedly closed with status 0

Isso acontece quando se tenta executar o script que anteriormente falhou e deixou o firefox aberto.
