[flake8]
max-line-length = 120
exclude = .git,__pycache__,build,dist
ignore =
    # Missing docstring
    D100,D101,D102,D103,D104,D105,D106,D107,
    # Invalid name
    N801,N802,N803,N804,N805,N806,N807,N811,N812,N813,N814,N815,N816,N817,N818,
    # Trailing whitespace
    W291,W292,W293,
    # Bad indentation
    E111,E112,E113,E114,E115,E116,E117,E118,E119,
    # Global statement
    W0601,W0602,W0603,W0604,
    # Redefined outer name
    W0621,W0622,W0623,W0624,
    # Too many arguments
    FBT001,FBT002,
    # Too many local variables
    FBT003,
    # Too many statements
    FBT004
per-file-ignores =
    __init__.py: F401
max-complexity = 10