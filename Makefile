# Makefile para Projeto Python MCP
# Fornece comandos para configurar e executar o projeto Python MCP

# Variáveis
PYTHON := python3.11
VENV := venv
VENV_PYTHON := $(VENV)/bin/python
VENV_PIP := $(VENV)/bin/pip
VENV_ACTIVATE := . $(VENV)/bin/activate

# Ferramentas de qualidade de código
BLACK := $(VENV)/bin/black
ISORT := $(VENV)/bin/isort
FLAKE8 := $(VENV)/bin/flake8
PYLINT := $(VENV)/bin/pylint
MYPY := $(VENV)/bin/mypy
PYTEST := $(VENV)/bin/pytest
AUTOFLAKE := $(VENV)/bin/autoflake

# Cores para saída do terminal
YELLOW := \033[1;33m
GREEN := \033[1;32m
NC := \033[0m # Sem Cor

# Alvo padrão
.PHONY: help
help:
	@echo "$(GREEN)Makefile do Projeto Python$(NC)"
	@echo "$(YELLOW)Executar:$(NC)"
	@echo "    $(YELLOW)run$(NC)             - executar o projeto"
	@echo ""
	@echo "$(YELLOW)Alvos disponíveis:$(NC)"
	@echo "    $(YELLOW)install$(NC)         - Configurar o projeto (criar venv e instalar dependências)"
	@echo "    $(YELLOW)uninstall$(NC)       - Remover o ambiente virtual, instalação e todos os arquivos de cache"
	@echo ""


.PHONY: remember-venv
remember-venv:
	@echo "$(YELLOW)Lembre-se de ativar o ambiente virtual com:$(NC) source $(VENV)/bin/activate"

.PHONY: run
run: remember-venv
	@echo "$(YELLOW)Use o comando: $(GREEN)python -m main --cnpj 17.784.050/0001-00 --ano 2025 --mes 5$(NC)"
	@echo ""

.PHONY: install
install:
	@echo "$(GREEN)Criando ambiente virtual...$(NC)"
	@$(PYTHON) -m venv $(VENV)
	@echo "$(GREEN)Ambiente virtual criado em $(VENV)$(NC)"
	@echo "$(GREEN)Instalando dependências...$(NC)"
	@$(VENV_PIP) install -r requirements.txt
	@echo "$(GREEN)Dependências instaladas$(NC)"
	@echo "$(GREEN)Configuração concluída!$(NC)"
	@echo "$(YELLOW)Ative o ambiente virtual com:$(NC) source $(VENV)/bin/activate"

.PHONY: uninstall
uninstall:
	@echo "$(GREEN)Desinstalando projeto Python...$(NC)"
	@echo "$(YELLOW)Removendo ambiente virtual...$(NC)"
	@rm -rf $(VENV)
	@echo "$(YELLOW)Removendo arquivos de cache Python...$(NC)"
	@find . -type d -name "__pycache__" -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete
	@find . -type f -name "*.pyo" -delete
	@echo "$(YELLOW)Removendo diretórios de build...$(NC)"
	@rm -rf build/ dist/ *.egg-info/
	@echo "$(YELLOW)Removendo arquivos de teste...$(NC)"
	@rm -rf .pytest_cache/ .coverage htmlcov/
	@echo "$(GREEN)Desinstalação concluída!$(NC)"
