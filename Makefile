# Makefile for MCP Python Project
# Provides commands for setting up and running the MCP Python project

# Variables
PYTHON := python3.11
VENV := venv
VENV_PYTHON := $(VENV)/bin/python
VENV_PIP := $(VENV)/bin/pip
VENV_ACTIVATE := . $(VENV)/bin/activate

# Code quality tools
BLACK := $(VENV)/bin/black
ISORT := $(VENV)/bin/isort
FLAKE8 := $(VENV)/bin/flake8
PYLINT := $(VENV)/bin/pylint
MYPY := $(VENV)/bin/mypy
PYTEST := $(VENV)/bin/pytest
AUTOFLAKE := $(VENV)/bin/autoflake

# Colors for terminal output
YELLOW := \033[1;33m
GREEN := \033[1;32m
NC := \033[0m # No Color

# Default target
.PHONY: help
help:
	@echo "$(GREEN)Python Project Makefile$(NC)"
	@echo "$(YELLOW)Available targets:$(NC)"
	@echo "  $(YELLOW)setup$(NC)           - Set up the project (create venv and install dependencies)"
	@echo "  $(YELLOW)venv$(NC)            - Create a virtual environment"
	@echo "  $(YELLOW)install$(NC)         - Install dependencies in the virtual environment"
	@echo "  $(YELLOW)clean$(NC)           - Clean up Python cache files"
	@echo "  $(YELLOW)uninstall$(NC)       - Remove the virtual environment, installation, and all cache files"
	@echo ""
	@echo "$(YELLOW)Code quality targets:$(NC)"
	@echo "  $(YELLOW)format$(NC)          - Format code with black and isort"
	@echo "  $(YELLOW)lint$(NC)            - Run linting with flake8 and pylint"
	@echo "  $(YELLOW)typecheck$(NC)       - Run type checking with mypy"
	@echo "  $(YELLOW)test$(NC)            - Run tests with pytest"
	@echo "  $(YELLOW)check-all$(NC)       - Run all code quality checks"
	@echo ""


# Setup targets
.PHONY: venv
venv:
	@echo "$(GREEN)Creating virtual environment...$(NC)"
	@$(PYTHON) -m venv $(VENV)
	@echo "$(GREEN)Virtual environment created at $(VENV)$(NC)"

.PHONY: install
install: venv
	@echo "$(GREEN)Installing dependencies...$(NC)"
	@$(VENV_PIP) install -r requirements.txt
	@echo "$(GREEN)Dependencies installed$(NC)"

.PHONY: setup
setup: venv install
	@echo "$(GREEN)Setup complete!$(NC)"
	@echo "$(YELLOW)Activate the virtual environment with:$(NC) source $(VENV)/bin/activate"


# Code quality targets
# IMPORTANT: These targets should be run OUTSIDE the virtual environment
# If you're in the virtual environment, run 'deactivate' first
.PHONY: format
format:
	@echo "$(GREEN)Formatting code with black and isort...$(NC)"
	@echo "$(YELLOW)⚠️  WARNING: This command should be run INSIDE the virtual environment.$(NC)"
	@sleep 2
	@$(BLACK) .
	@$(ISORT) .
	@echo "$(GREEN)Formatting complete!$(NC)"

.PHONY: lint
lint:
	@echo "$(GREEN)Running flake8...$(NC)"
	@echo "$(YELLOW)⚠️  WARNING: This command should be run INSIDE the virtual environment.$(NC)"
	@sleep 2
	@$(FLAKE8) .
	@echo "$(GREEN)Running pylint...$(NC)"
	@$(PYLINT) cli/ollama_chat.py
	@echo "$(GREEN)Linting complete!$(NC)"

.PHONY: typecheck
typecheck:
	@echo "$(GREEN)Running mypy type checking...$(NC)"
	@echo "$(YELLOW)⚠️  WARNING: This command should be run INSIDE the virtual environment.$(NC)"
	@sleep 2
	@$(MYPY) .
	@echo "$(GREEN)Type checking complete!$(NC)"

.PHONY: test
test:
	@echo "$(GREEN)Running tests with pytest...$(NC)"
	@$(PYTEST)
	@echo "$(GREEN)Tests complete!$(NC)"

.PHONY: check-all
check-all: format lint typecheck test
	@echo "$(GREEN)All checks completed successfully!$(NC)"

.PHONY: remove-unused
remove-unused:
	@echo "$(GREEN)Removing unused imports and variables...$(NC)"
	@echo "$(YELLOW)⚠️  WARNING: This command should be run INSIDE the virtual environment.$(NC)"
	@sleep 2
	@$(AUTOFLAKE) --in-place --remove-unused-variables --remove-all-unused-imports --recursive .
	@echo "$(GREEN)Unused code removal complete!$(NC)"

.PHONY: format-all
format-all: remove-unused format
	@echo "$(GREEN)All formatting complete!$(NC)"

# Cleanup targets
.PHONY: clean
clean:
	@echo "$(GREEN)Cleaning up project...$(NC)"
	@echo "$(YELLOW)Removing Python cache files...$(NC)"
	@find . -type d -name "__pycache__" -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete
	@echo "$(GREEN)Cleanup complete!$(NC)"

.PHONY: uninstall
uninstall:
	@echo "$(GREEN)Uninstalling Python project...$(NC)"
	@echo "$(YELLOW)Removing virtual environment...$(NC)"
	@rm -rf $(VENV)
	@echo "$(GREEN)Uninstallation complete!$(NC)"
