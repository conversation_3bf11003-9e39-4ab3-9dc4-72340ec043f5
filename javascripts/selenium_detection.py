def script_selenium_detection() -> str:
    return """
        let detected = false;

        // Verificações comuns para detecção de automação
        if (window.navigator.webdriver) detected = true;
        if (window._phantom) detected = true;
        if (window.callPhantom) detected = true;
        if (window.__nightmare) detected = true;
        if (document.__selenium_unwrapped) detected = true;
        if (document.__webdriver_script_fn) detected = true;
        if (document.$cdc_asdjflasutopfhvcZLmcfl_) detected = true;

        // Verifica objetos globais específicos do Selenium
        try {
            // Testes adicionais que sites podem executar
            const t = window.top;
            const d = window.document;
            const k = [
                'd.createElement("p").innerHTML',
                't.document.documentElement.getAttribute("webdriver")',
                't.document.documentElement.getAttribute("webdriver-evaluate")',
                't.document.documentElement.getAttribute("selenium")',
                't.document.documentElement.getAttribute("driver")',
                't.navigator.webdriver'
            ];

            for (let i = 0; i < k.length; i++) {
                try {
                    if (eval(k[i])) {
                        detected = true;
                        break;
                    }
                } catch (e) {}
            }
        } catch (e) {}

        return {
            detected: detected,
            userAgent: navigator.userAgent,
            webdriver: navigator.webdriver,
            plugins: navigator.plugins.length,
            languages: navigator.languages,
            platform: navigator.platform,
            hardwareConcurrency: navigator.hardwareConcurrency
        };
    """
