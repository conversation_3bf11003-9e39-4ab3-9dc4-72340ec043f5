"""Automação de navegador com Selenium para acessar o portal do empregador."""

import argparse
import os
import shutil
import sys
import time
import zipfile
from datetime import datetime

from dotenv import load_dotenv
from selenium import webdriver
from selenium.common.exceptions import NoSuchElementException, TimeoutException, WebDriverException
from selenium.webdriver.firefox.options import Options
from selenium.webdriver.firefox.service import Service

# Import GeckoDriverManager conditionally to avoid issues on ARM systems
try:
    from webdriver_manager.firefox import GeckoDriverManager

    GECKODRIVER_MANAGER_AVAILABLE = True
except ImportError:
    GECKODRIVER_MANAGER_AVAILABLE = False
    print("AVISO: webdriver_manager não disponível. Usando geckodriver local.")

from goto.accept_terms import executar_aceitar_termos_e_condicoes
from goto.download import DownloadFailureReason, executar_download
from goto.login import executar_login
from goto.selecionar_empresa import SelecionarEmpresaFailureReason, executar_selecionar_empresa
from javascripts.hide_automation import script_hide_automation
from javascripts.selenium_detection import script_selenium_detection
from utils import random_delay


def install_extension_alternative_1(driver: webdriver.Firefox, xpi_path: str) -> bool:
    """Alternative method 1: Copy to working directory with unique name."""
    temp_path = None
    try:
        # Create a unique filename in the current working directory
        import uuid

        unique_id = str(uuid.uuid4())[:8]
        temp_filename = f"temp_extension_{unique_id}.xpi"
        temp_path = os.path.join(os.getcwd(), temp_filename)

        # Copy the XPI file to working directory
        shutil.copy2(xpi_path, temp_path)

        print(f"Copied XPI to working directory: {temp_path}")
        print(f"Temp file size: {os.path.getsize(temp_path)} bytes")

        # Verify the file is accessible
        if not os.access(temp_path, os.R_OK):
            print(f"Temp file is not readable: {temp_path}")
            return False

        # Install from working directory location
        driver.install_addon(os.path.abspath(temp_path), temporary=True)

        return True

    except Exception as e:
        print(f"Alternative method 1 failed: {e}")
        return False
    finally:
        # Clean up temp file if it exists
        if temp_path and os.path.exists(temp_path):
            try:
                os.unlink(temp_path)
                print(f"Cleaned up temp file: {temp_path}")
            except Exception as cleanup_error:
                print(f"Warning: Could not clean up temp file {temp_path}: {cleanup_error}")


def install_extension_alternative_2(driver: webdriver.Firefox, xpi_path: str) -> bool:
    """Alternative method 2: Copy to downloads directory."""
    temp_path = None
    try:
        # Use downloads directory as it's guaranteed to be writable
        import uuid

        unique_id = str(uuid.uuid4())[:8]
        temp_filename = f"temp_extension_{unique_id}.xpi"
        temp_path = os.path.join(DOWNLOADS_DIR, temp_filename)

        # Copy the XPI file to downloads directory
        shutil.copy2(xpi_path, temp_path)

        print(f"Copied XPI to downloads directory: {temp_path}")
        print(f"Temp file size: {os.path.getsize(temp_path)} bytes")

        # Install from downloads directory
        driver.install_addon(os.path.abspath(temp_path), temporary=True)

        return True

    except Exception as e:
        print(f"Alternative method 2 failed: {e}")
        return False
    finally:
        # Clean up temp file if it exists
        if temp_path and os.path.exists(temp_path):
            try:
                os.unlink(temp_path)
                print(f"Cleaned up temp file: {temp_path}")
            except Exception as cleanup_error:
                print(f"Warning: Could not clean up temp file {temp_path}: {cleanup_error}")


def install_extension_alternative_3(driver: webdriver.Firefox, xpi_path: str) -> bool:
    """Alternative method 3: Try with different file permissions."""
    try:
        # Make sure the original file has proper permissions
        abs_path = os.path.abspath(xpi_path)

        # Set more permissive permissions temporarily
        original_mode = os.stat(abs_path).st_mode
        os.chmod(abs_path, 0o755)

        print(f"Set permissive permissions on: {abs_path}")

        # Try to install with more permissive permissions
        driver.install_addon(abs_path, temporary=True)

        # Restore original permissions
        os.chmod(abs_path, original_mode)

        return True

    except Exception as e:
        print(f"Alternative method 3 failed: {e}")
        # Try to restore original permissions even if installation failed
        try:
            if "original_mode" in locals():
                os.chmod(abs_path, original_mode)
        except:
            pass
        return False


def diagnose_tmp_directory():
    """Diagnose issues with /tmp directory."""
    print("\n--- Diagnosing /tmp directory issues ---")

    import tempfile

    tmp_dir = tempfile.gettempdir()
    print(f"System temp directory: {tmp_dir}")
    print(f"Temp dir exists: {os.path.exists(tmp_dir)}")
    print(f"Temp dir writable: {os.access(tmp_dir, os.W_OK)}")
    print(f"Temp dir readable: {os.access(tmp_dir, os.R_OK)}")

    # Try to create a test file in /tmp
    try:
        test_file = os.path.join(tmp_dir, "selenium_test.txt")
        with open(test_file, "w") as f:
            f.write("test")
        print(f"✅ Can create files in {tmp_dir}")
        os.unlink(test_file)
        print(f"✅ Can delete files in {tmp_dir}")
    except Exception as e:
        print(f"❌ Cannot create/delete files in {tmp_dir}: {e}")

    # Check disk space
    try:
        import shutil

        total, used, free = shutil.disk_usage(tmp_dir)
        print(f"Disk space - Total: {total//1024//1024}MB, Used: {used//1024//1024}MB, Free: {free//1024//1024}MB")
    except Exception as e:
        print(f"❌ Cannot check disk space: {e}")


def install_extension_via_profile(driver: webdriver.Firefox, xpi_path: str) -> bool:
    """Alternative: Install extension by copying to Firefox profile."""
    try:
        print("Attempting profile-based extension installation...")

        # Get Firefox profile directory
        profile_dir = FIREFOX_PROFILE
        extensions_dir = os.path.join(profile_dir, "extensions")

        # Create extensions directory if it doesn't exist
        os.makedirs(extensions_dir, exist_ok=True)

        # Generate extension ID (simplified approach)
        import uuid

        extension_id = f"stealth-extension@{str(uuid.uuid4())[:8]}.xpi"
        target_path = os.path.join(extensions_dir, extension_id)

        # Copy extension to profile
        shutil.copy2(xpi_path, target_path)
        print(f"✅ Copied extension to profile: {target_path}")

        # This requires browser restart to take effect
        print("⚠️  Extension will be active after browser restart")
        return True

    except Exception as e:
        print(f"❌ Profile-based installation failed: {e}")
        return False


def install_extension_via_javascript(driver: webdriver.Firefox, xpi_path: str) -> bool:
    """Alternative: Inject stealth scripts directly via JavaScript."""
    try:
        print("Attempting JavaScript-based stealth injection...")

        # Read the stealth.js content
        stealth_js_path = os.path.join(EXTENSION_PATH, "stealth.js")
        if not os.path.exists(stealth_js_path):
            print(f"❌ Stealth script not found: {stealth_js_path}")
            return False

        with open(stealth_js_path, "r", encoding="utf-8") as f:
            stealth_script = f.read()

        # Execute the stealth script directly
        driver.execute_script(stealth_script)
        print("✅ Stealth scripts injected via JavaScript")

        # Also execute our existing hide automation script
        from javascripts.hide_automation import script_hide_automation

        driver.execute_script(script_hide_automation())
        print("✅ Additional anti-detection scripts executed")

        return True

    except Exception as e:
        print(f"❌ JavaScript injection failed: {e}")
        return False


def install_extension_with_custom_tmp(driver: webdriver.Firefox, xpi_path: str) -> bool:
    """Alternative: Set custom temp directory and try installation."""
    import tempfile

    original_tmpdir = None
    custom_tmpdir = None

    try:
        print("Attempting installation with custom temp directory...")

        # Create a custom temp directory in our working space
        custom_tmpdir = os.path.join(os.getcwd(), "custom_tmp")
        os.makedirs(custom_tmpdir, exist_ok=True)

        # Backup original TMPDIR
        original_tmpdir = os.environ.get("TMPDIR")

        # Set custom temp directory
        os.environ["TMPDIR"] = custom_tmpdir
        os.environ["TMP"] = custom_tmpdir
        os.environ["TEMP"] = custom_tmpdir

        print(f"Set custom temp directory: {custom_tmpdir}")

        # Try installation with custom temp directory
        driver.install_addon(os.path.abspath(xpi_path), temporary=True)

        return True

    except Exception as e:
        print(f"❌ Custom temp directory method failed: {e}")
        return False
    finally:
        # Restore original temp directory settings
        if original_tmpdir:
            os.environ["TMPDIR"] = original_tmpdir
        else:
            os.environ.pop("TMPDIR", None)
        os.environ.pop("TMP", None)
        os.environ.pop("TEMP", None)

        # Clean up custom temp directory
        if custom_tmpdir and os.path.exists(custom_tmpdir):
            try:
                import shutil

                shutil.rmtree(custom_tmpdir)
                print(f"Cleaned up custom temp directory: {custom_tmpdir}")
            except Exception as cleanup_error:
                print(f"Warning: Could not clean up {custom_tmpdir}: {cleanup_error}")


def robust_extension_install(driver: webdriver.Firefox, xpi_path: str) -> bool:
    """Try multiple methods to install the extension."""
    print("Attempting robust extension installation...")

    # First, diagnose the /tmp directory issue
    diagnose_tmp_directory()

    methods = [
        ("Standard method", lambda: driver.install_addon(os.path.abspath(xpi_path), temporary=True)),
        ("Alternative 1 (working dir copy)", lambda: install_extension_alternative_1(driver, xpi_path)),
        ("Alternative 2 (downloads dir copy)", lambda: install_extension_alternative_2(driver, xpi_path)),
        ("Alternative 3 (permissions fix)", lambda: install_extension_alternative_3(driver, xpi_path)),
        ("Alternative 4 (custom temp dir)", lambda: install_extension_with_custom_tmp(driver, xpi_path)),
        ("Alternative 5 (JavaScript injection)", lambda: install_extension_via_javascript(driver, xpi_path)),
    ]

    for method_name, method_func in methods:
        try:
            print(f"\nTrying {method_name}...")
            result = method_func()
            if result is not False:  # Success or no explicit return
                print(f"✅ {method_name} succeeded!")
                return True
        except Exception as e:
            print(f"❌ {method_name} failed: {e}")
            continue

    print("\n❌ All extension installation methods failed")
    print("⚠️  Continuing without extension - detection may be more likely")
    return False


# Parse command line arguments
parser = argparse.ArgumentParser(
    description="Automação para acessar o portal do empregador e obter arquivos de empréstimo"
)
parser.add_argument(
    "--cnpj",
    type=str,
    required=True,
    help="CNPJ da empresa a ser selecionada (formato: XX.XXX.XXX/XXXX-XX)",
)
parser.add_argument(
    "--ano",
    type=int,
    required=True,
    help="Ano para download dos arquivos (formato: YYYY)",
)
parser.add_argument(
    "--mes",
    type=int,
    required=True,
    choices=range(1, 13),
    help="Mês para download dos arquivos (1-12)",
)
parser.add_argument(
    "--firefox-profile",
    type=str,
    required=True,
    help="Caminho para o perfil do Firefox. Exemplo: /home/<USER>/.mozilla/firefox/3kaiezae.teste-zimps",
)
args = parser.parse_args()

# Get CNPJ from command line
target_cnpj = args.cnpj
target_ano = args.ano
target_mes = args.mes

# Não tem porque deixar colocar um ano muito antigo, pois o site não tem dados anteriores a 2000.
if target_ano < 2000 or target_ano > datetime.now().year:
    print(f"Ano inválido: {target_ano}. Deve estar entre 2000 e {datetime.now().year}")
    sys.exit(1)

# Carrega variáveis de ambiente
load_dotenv()

# URL alvo direta para a página de arquivos de empréstimos
TARGET_URL = "https://servicos.mte.gov.br/"

SELECAO_EMPRESA_URL = "https://servicos.mte.gov.br/empregador/#/selecao-empresa-vinculada"

# Configura o perfil do Firefox
FIREFOX_PROFILE = args.firefox_profile

# Verifica se o diretório do perfil existe
if not os.path.isdir(FIREFOX_PROFILE):
    print(f"Erro: O diretório de perfil do Firefox não existe: {FIREFOX_PROFILE}")
    sys.exit(1)

# Diretório para downloads
DOWNLOADS_DIR = "downloads"
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

# Diretório para screenshots
SCREENSHOTS_DIR = "screenshots"
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

# Diretório para extensões
EXTENSIONS_DIR = "extensions"
os.makedirs(EXTENSIONS_DIR, exist_ok=True)

# Criar extensão para interceptar e redefinir navigator.webdriver
EXTENSION_PATH = os.path.join(EXTENSIONS_DIR, "stealth_extension")
os.makedirs(EXTENSION_PATH, exist_ok=True)

# Empacotar a extensão como xpi (formato de extensão do Firefox)
XPI_PATH = os.path.join(EXTENSIONS_DIR, "stealth_extension.xpi")

# Criar o arquivo xpi
if not os.path.isfile(XPI_PATH):
    manifest_path = os.path.join(EXTENSION_PATH, "manifest.json")
    stealth_path = os.path.join(EXTENSION_PATH, "stealth.js")

    if not os.path.exists(manifest_path) or not os.path.exists(stealth_path):
        print(f"Erro: Arquivos da extensão não encontrados em {EXTENSION_PATH}")
        print(f"  - manifest.json existe: {os.path.exists(manifest_path)}")
        print(f"  - stealth.js existe: {os.path.exists(stealth_path)}")
        sys.exit(1)

    print(f"Criando arquivo XPI: {XPI_PATH}")
    try:
        with zipfile.ZipFile(XPI_PATH, "w", zipfile.ZIP_STORED) as xpi:
            xpi.write(manifest_path, "manifest.json")
            xpi.write(stealth_path, "stealth.js")

        # Garantir que o arquivo XPI tem permissões corretas
        os.chmod(XPI_PATH, 0o644)

        # Verificar se o arquivo foi criado corretamente
        if os.path.exists(XPI_PATH) and os.path.getsize(XPI_PATH) > 0:
            print(f"Arquivo XPI criado com sucesso: {os.path.getsize(XPI_PATH)} bytes")
        else:
            print("Erro: Falha ao criar arquivo XPI válido")
            sys.exit(1)

    except Exception as e:
        print(f"Erro ao criar arquivo XPI: {e}")
        sys.exit(1)
else:
    print(f"Arquivo XPI já existe: {XPI_PATH} ({os.path.getsize(XPI_PATH)} bytes)")

# Configurar opções do Firefox
options = Options()
options.add_argument("-profile")
options.add_argument(FIREFOX_PROFILE)
options.set_capability("acceptInsecureCerts", True)
options.set_preference("browser.privatebrowsing.autostart", False)  # Desabilita modo de navegação privada

# Configurações anti-detecção
options.set_preference("dom.webdriver.enabled", False)
options.set_preference("useAutomationExtension", False)
options.set_preference("marionette", False)
options.set_preference(
    "general.useragent.override",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0",
)

# Configurações adicionais anti-detecção
options.set_preference("webdriver.load.strategy", "eager")
options.set_preference("javascript.enabled", True)
options.set_preference("media.navigator.permission.disabled", True)
options.set_preference("media.peerconnection.enabled", False)
options.set_preference("privacy.trackingprotection.enabled", False)
options.set_preference("network.http.referer.spoofSource", True)
options.set_preference("dom.webnotifications.enabled", False)
options.set_preference("permissions.default.desktop-notification", 2)

# Configurações avançadas anti-detecção (firefox_profile)
# Simule um comportamento de usuário real com histórico e cookies
options.set_preference("places.history.enabled", True)
options.set_preference("browser.cache.disk.enable", True)
options.set_preference("browser.cache.memory.enable", True)
options.set_preference("browser.sessionhistory.max_entries", 25)
options.set_preference("network.cookie.cookieBehavior", 0)  # Aceitar todos os cookies

# Simule plugins comuns
options.set_preference("media.navigator.enabled", True)
options.set_preference("media.navigator.video.enabled", True)
options.set_preference("media.video_stats.enabled", True)

# Defina uma resolução comum de tela e cores
options.set_preference("layout.css.devPixelsPerPx", "1.0")
options.set_preference("browser.display.background_color", "#ffffff")
options.set_preference("browser.display.foreground_color", "#000000")

# Simular comportamento de fontes reais
options.set_preference("font.internaluseonly.changed", False)
options.set_preference("browser.display.use_document_fonts", 1)

# Configurações de timezone Brasil
options.set_preference("intl.accept_languages", "pt-BR, pt")
options.set_preference("intl.locale.matchOS", False)
options.set_preference("intl.locale.requested", "pt-BR")

# Configurar a extensão para o Firefox
options.set_preference("xpinstall.signatures.required", False)

# Verifica se deve executar em modo headless
HEADLESS = os.getenv("HEADLESS", "false").lower() == "true"
if HEADLESS:
    options.add_argument("--headless")
    print("HEADLESS MODE ON")
else:
    print("HEADLESS MODE OFF")

# Configurar pasta de downloads
options.set_preference("browser.download.folderList", 2)
options.set_preference("browser.download.dir", os.path.abspath(DOWNLOADS_DIR))
options.set_preference("browser.download.useDownloadDir", True)
options.set_preference("browser.download.viewableInternally.enabledTypes", "")
options.set_preference("browser.helperApps.neverAsk.saveToDisk", "application/json,text/json")

# Adicionar nova configuração
options.set_preference("security.default_personal_cert", "Select Automatically")

# Adicionar nova configuração
options.set_preference("permissions.default.geo", 2)  # 2 = Block, 1 = Allow, 0 = Always ask

# Registra o timestamp de início
start_time = datetime.now()
print(f"Script iniciado em: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

# Informações de debug sobre o ambiente
print(f"Diretório de trabalho atual: {os.getcwd()}")
print(f"Caminho do perfil Firefox: {FIREFOX_PROFILE}")
print(f"Diretório de downloads: {os.path.abspath(DOWNLOADS_DIR)}")
print(f"Diretório de extensões: {os.path.abspath(EXTENSIONS_DIR)}")
print(f"Modo headless: {HEADLESS}")

try:
    # Inicializa o driver do Firefox

    print(f"Arquitetura do sistema: {os.uname().machine}")

    if os.uname().machine == "aarch64" or not GECKODRIVER_MANAGER_AVAILABLE:
        print("Usando geckodriver local...")
        service = Service("/usr/local/bin/geckodriver")
    else:
        print("Usando GeckoDriverManager para baixar geckodriver...")
        service = Service(GeckoDriverManager().install())

    driver = webdriver.Firefox(options=options, service=service)

    # Instalar a extensão (feito depois de inicializar o driver)
    # Verifica se o arquivo fonte existe
    if not os.path.exists(XPI_PATH):
        print(f"Erro: Arquivo fonte da extensão não encontrado: {XPI_PATH}")
        print("AVISO: Continuando execução sem a extensão stealth...")
        print("A detecção de automação pode ser mais provável.")
    else:
        # Obter o caminho absoluto e verificar se é acessível
        abs_xpi_path = os.path.abspath(XPI_PATH)
        print(f"Caminho absoluto da extensão: {abs_xpi_path}")

        # Verificar se o arquivo é legível
        if not os.access(abs_xpi_path, os.R_OK):
            print(f"Erro: Arquivo da extensão não é legível: {abs_xpi_path}")
            print("AVISO: Continuando execução sem a extensão stealth...")
            print("A detecção de automação pode ser mais provável.")
        else:
            # Verificar o tamanho do arquivo
            file_size = os.path.getsize(abs_xpi_path)
            print(f"Tamanho do arquivo da extensão: {file_size} bytes")

            if file_size == 0:
                print(f"Erro: Arquivo da extensão está vazio: {abs_xpi_path}")
                print("AVISO: Continuando execução sem a extensão stealth...")
                print("A detecção de automação pode ser mais provável.")
            else:
                # Tentar instalar a extensão usando métodos robustos
                success = robust_extension_install(driver, XPI_PATH)

                if not success:
                    print("AVISO: Falha ao instalar extensão. Continuando sem ela...")
                    print("A detecção de automação pode ser mais provável.")

    # Tamanho da janela e posição mais "natural"
    driver.set_window_size(1366, 768)
    driver.set_window_position(0, 0)

    # Executando comandos CDP para mascarar navegação automatizada
    driver.execute_script(script_hide_automation())

    # Captura screenshot antes de continuar
    driver.save_screenshot(f"{SCREENSHOTS_DIR}/before_login.png")

    # Abre o navegador e navega numa URL do site, para alimentar o histórico do navegador e evitar detecção.
    driver.get(TARGET_URL)
    print(f"Navegador aberto na URL: {TARGET_URL}")

    # Espera 10 segundos independente se carregou tudo.
    # As vezes, a página fica esperando algum script inútil terminar.
    time.sleep(5)

    # Insere algum código JavaScript para verificar se o Selenium está sendo detectado
    detection_result = driver.execute_script(script_selenium_detection())

    print("Resultado da verificação de detecção:")
    print(f"  - Automação detectada: {detection_result.get('detected', 'N/A')}")
    print(f"  - User Agent: {detection_result.get('userAgent', 'N/A')}")
    print(f"  - Webdriver: {detection_result.get('webdriver', 'N/A')}")
    print(f"  - Plugins: {detection_result.get('plugins', 'N/A')}")
    print(f"  - Languages: {detection_result.get('languages', 'N/A')}")
    print(f"  - Platform: {detection_result.get('platform', 'N/A')}")
    print(f"  - Hardware Concurrency: {detection_result.get('hardwareConcurrency', 'N/A')}")

    # Caso a automação seja detectada, não adianta continuar.
    if detection_result.get("detected", False):
        print("**** Automação detectada. Encerrando execução. ****")
        driver.quit()
        sys.exit(1)

    random_delay(3, 7)

    # Para simular comportamento humano, vamos fazer alguns movimentos e scroll
    driver.execute_script("window.scrollBy(0, 300);")
    random_delay(1, 3)

    driver.execute_script("window.scrollBy(0, -150);")
    random_delay(1, 2)

    # Cenário otimista mas com pé no chão:
    # - Verifica se apareceu os termos e condições.
    # - Tentar executar o download diretamente.
    # - Se falhar, tentar autenticar primeiro.
    # - Autenticado, ir para etapa 2 que ele mesmo irá redirecionar para a página de download.

    SUCESSO, REASON = executar_download(driver, target_cnpj, target_ano, target_mes)

    if SUCESSO:
        print("MAIN -> `executar_download` bem sucedido.")
        print("MAIN -> Finalizando execução.")
        driver.quit()
        sys.exit(0)
    elif not SUCESSO:
        if REASON == DownloadFailureReason.REDIRECT_TO_LOGIN_PAGE:
            print("MAIN -> `executar_download` falhou. Tentando autenticar.")
            executar_login(driver)
            random_delay(2, 5)
            SUCCESS, COMPANY_REASON = executar_selecionar_empresa(driver, target_cnpj)
            if SUCCESS:
                print("MAIN -> `executar_selecionar_empresa` bem sucedido.")
                print("MAIN -> Finalizando execução.")
                SUCESSO, REASON = executar_download(driver, target_cnpj, target_ano, target_mes)
                if SUCESSO:
                    print("MAIN -> `executar_download` bem sucedido.")
                    print("MAIN -> Finalizando execução.")
                    driver.quit()
                    sys.exit(0)
                else:
                    print(f"MAIN -> `executar_download` falhou. Razão: {REASON.name}. Encerrando execução.")
                    driver.quit()
                    sys.exit(1)
            else:
                print(
                    f"MAIN -> `executar_selecionar_empresa` falhou. Razão: {COMPANY_REASON.name}. Encerrando execução."
                )
                driver.quit()
                sys.exit(1)
        elif REASON == DownloadFailureReason.REDIRECT_TO_COMPANY_PAGE:
            print("MAIN -> `executar_download` falhou. Tentando selecionar empresa.")
            SUCCESS, COMPANY_REASON = executar_selecionar_empresa(driver, target_cnpj)
            if SUCCESS:
                print("MAIN -> `executar_selecionar_empresa` bem sucedido.")
                print("MAIN -> Finalizando execução.")
                SUCESSO, REASON = executar_download(driver, target_cnpj, target_ano, target_mes)
                if SUCESSO:
                    print("MAIN -> `executar_download` bem sucedido.")
                    print("MAIN -> Finalizando execução.")
                    driver.quit()
                    sys.exit(0)
                else:
                    print(f"MAIN -> `executar_download` falhou. Razão: {REASON.name}. Encerrando execução.")
                    driver.quit()
                    sys.exit(1)
            else:
                if COMPANY_REASON == SelecionarEmpresaFailureReason.MODAL_TERMS_AND_CONDITIONS:
                    print(
                        f"MAIN -> `executar_selecionar_empresa` falhou. Razão: {COMPANY_REASON.name}. Aceitando termos e condições e tentando novamente."
                    )
                    SUCCESS = executar_aceitar_termos_e_condicoes(driver)
                    if SUCCESS:
                        print("MAIN -> `executar_aceitar_termos_e_condicoes` bem sucedido.")
                        print("MAIN -> Finalizando execução.")
                        SUCESSO, REASON = executar_download(driver, target_cnpj, target_ano, target_mes)
                        if SUCESSO:
                            print("MAIN -> `executar_download` bem sucedido.")
                            print("MAIN -> Finalizando execução.")
                            driver.quit()
                            sys.exit(0)
                        else:
                            print(f"MAIN -> `executar_download` falhou. Razão: {REASON.name}. Encerrando execução.")
                            driver.quit()
                            sys.exit(1)
                    else:
                        print(
                            f"MAIN -> `executar_aceitar_termos_e_condicoes` falhou. Razão: {COMPANY_REASON.name}. Encerrando execução."
                        )
                        driver.quit()
                        sys.exit(1)
                else:
                    print(
                        f"MAIN -> `executar_selecionar_empresa` falhou. Razão: {COMPANY_REASON.name}. Encerrando execução."
                    )
                    driver.quit()
                    sys.exit(1)
        else:
            print(f"MAIN -> `executar_download` falhou. Razão: {REASON.name}. Encerrando execução.")
            driver.quit()
            sys.exit(1)

except NoSuchElementException as e:  # não deveria acontecer, culpa do programador.
    print(f"Algum `NoSuchElementException` escapou ao executar as rotinas: {e}")
except TimeoutException as e:  # programador precisa rever o código.
    print(f"`TimeoutException`: {e}")
except WebDriverException as e:  # erro realmente grave, não é culpa do programador.
    print(f"`WebDriverException`: {e}")
finally:
    try:
        # Registra o timestamp de fim
        end_time = datetime.now()
        print(f"Script finalizado em: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Tempo de execução total: {end_time - start_time}")

        # Tenta fechar o navegador se ele ainda estiver aberto
        if "driver" in locals() and driver:
            driver.quit()
    except (WebDriverException, RuntimeError) as e:
        print(f"Erro ao finalizar: {e}")
