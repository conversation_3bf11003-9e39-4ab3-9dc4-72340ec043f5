"""Main entry point for browser automation with <PERSON><PERSON> and dotenv."""

from dotenv import load_dotenv
from playwright.sync_api import sync_playwright
import os
from datetime import datetime
import time

load_dotenv()

TARGET_URL = "https://servicos.mte.gov.br/empregador"
FIREFOX_PROFILE = "/home/<USER>/.mozilla/firefox/3kaiezae.teste-zimps"

# Create screenshots directory if it doesn't exist
SCREENSHOTS_DIR = "screenshots"
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

with sync_playwright() as p:
    # Launch Firefox with the existing profile
    browser = p.firefox.launch_persistent_context(
        FIREFOX_PROFILE,
        headless=False,  # Set to True for headless mode
        ignore_https_errors=True,
    )

    # The certificate should already be installed in this profile

    page = browser.new_page()
    page.goto(TARGET_URL)

    # Extract the title tag and print it
    title = page.title()
    print(f"Page title: {title}")

    # Click the login button
    print("Clicking login button...")
    login_button = page.locator("button.br-button.sign-in")
    login_button.click()

    # Wait for navigation after clicking the login button
    page.wait_for_load_state("domcontentloaded")
    page.wait_for_load_state("networkidle")
    time.sleep(3)

    # Print the new page title after redirection
    new_title = page.title()
    print(f"New page title after login: {new_title}")
    print(f"New URL: {page.url}")

    # Click on the digital certificate button
    print("Clicking on digital certificate button...")
    cert_button = page.locator("#login-certificate")
    cert_button.click()

    # More robust waiting for page to load
    page.wait_for_load_state("domcontentloaded")
    page.wait_for_load_state("networkidle")

    # Additional delay to ensure all visual elements are rendered
    time.sleep(3)

    # Print the title after certificate authentication page loads
    cert_page_title = page.title()
    print(f"Certificate page title: {cert_page_title}")
    print(f"Certificate page URL: {page.url}")

    # Take a screenshot
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"certificate_page_{timestamp}.png"
    screenshot_path = os.path.join(SCREENSHOTS_DIR, filename)
    page.screenshot(path=screenshot_path, full_page=True)
    print(f"Screenshot saved to: {screenshot_path}")

    # browser.close()
    # Keep the browser open for manual interaction
    print("\nBrowser is kept open for captcha solving.")
    print("Press Ctrl+C in the terminal when you want to close the script.")

    # Wait indefinitely until the user terminates the script
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nScript terminated by user.")
