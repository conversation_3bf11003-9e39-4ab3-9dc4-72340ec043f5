# Disabled warnings:
# C0111: Missing docstring
# C0103: Invalid name
# C0303: Trailing whitespace
# W0311: Bad indentation
# W0603: Global statement
# W0621: Redefined outer name
# R0903: Too few public methods
# R0913: Too many arguments
# R0914: Too many local variables
# R0915: Too many statements
# R1705: Unnecessary else after return

[MASTER]
disable=C0111,C0103,C0303,W0311,W0603,W0621,R0903,R0913,R0914,R0915,R1705

[MESSAGES CONTROL]
disable=C0111,C0103,C0303,W0311,W0603,W0621,R0903,R0913,R0914,R0915,R1705

[FORMAT]
max-line-length=120
indent-string='    '

[BASIC]
good-names=i,j,k,ex,Run,_,id,db

[SIMILARITIES]
min-similarity-lines=4
ignore-comments=yes
ignore-docstrings=yes
ignore-imports=yes

[TYPECHECK]
ignore-mixin-members=yes
ignored-classes=SQLObject
unsafe-load-any-extension=yes

[VARIABLES]
init-import=no
dummy-variables-rgx=_$|dummy
additional-builtins=

[CLASSES]
defining-attr-methods=__init__,__new__,setUp
valid-classmethod-first-arg=cls
valid-metaclass-classmethod-first-arg=mcs

[DESIGN]
max-args=5
max-attributes=7
min-public-methods=2
max-public-methods=20

[IMPORTS]
deprecated-modules=regsub,TERMIOS,Bastion,rexec
import-graph=
ext-import-graph=
int-import-graph=

[EXCEPTIONS]
overgeneral-exceptions=Exception
