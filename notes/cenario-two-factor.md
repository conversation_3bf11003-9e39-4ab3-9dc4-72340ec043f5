# Cenários

Ao navegar pelo site, é possível que ocorra coisas estranhas e inesperadas.

Ao autenticar, talvez esteja habilitado o fator duplo de autenticação.

Neste caso:

- URL será: `https://certificado.sso.acesso.gov.br/login?client_id=empregador.servicos.mte.gov.br&authorization_id=196d60bd8d0`

![Descrição da imagem](image.png)

```html
<form id="twoFactorForm" action="https://sso.acesso.gov.br/login?client_id=empregador.servicos.mte.gov.br&amp;authorization_id=196d60bd8d0" method="post" autocomplete="off" novalidate="">
   <div class="card" id="two-factor">
      <input type="hidden" name="_csrf" value="026cf461-a405-42af-9bf6-2394053fe136">
      <input type="hidden" name="token" value="eyJraWQiOiJsb2dpbkZsb3ciLCJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..56hhj_OFlFXP3JkX.EiwDN3A13dj8sUYSzFrwZYOMbHhYbRwLsmcSjNXBh4J5xKeg_4M-h1X2QZMucu_4HfuZEL_3P4sv711J0L-ZCv6i6qSBQhqjGCZiUdm5NjhPSbiqJbhv2JAPPTPkv8a8kI1zcJgZtySdHQZBo8Zo1TPr9puNwLe28SGpavoKCw6XTQwREn2-oqPAEYj6BdtmIXy-OQML2IDkWsof0r05SizDYiy1Y9IwbmUHvmhYUyyeg09UMWYk16gqeMOzKnPUirMZKQf5zHOqp_IdcIZqR6bqX63lzsOr3VnoQQ2tNerq9rjBJ05bqFnOc6AWWVPlyNi5gLMU5pWQdydN6J6wSxu4T7fw5fXZJeU9e67OMQdknDEOqUjqbfm5CwELOQzAnse-tlF_CvfEWRwMCZLvUDSE7kAPbjT4CR2Dnyx4vg1g_p2266_jI6TEP1Y9SWkfXY061RzehLMGYN-FoO3gO0AB1ZRDfxhLdiJaSOVi1r7BIhHwLnBSUdgbgljpyUujq1ZfTmDdOxgNtVuGERPKOQWw1wyXGLtA06EJO_d16l-aJDGsjOLp_EUuc4bnHSbDZkxgxNueECY-mRy5F426WtntIDgtOnKCpQllXtruEHBZJ1L1cb_N4hfwQtDXBUmhRi-Xpls7EjK0YNPaOBsQKO3EukuiRHq6hZcCMxA_aTdYkpMGTlN81Iay3aYxJrEUIWnTg-8WkUZHwAuk7aiFeE4zn9jo1GpGAiSw2AIsXeZwvF_j6QmPHHqEadxZmomzDisurx5OT5HlM0UCEqtUEpMFvrY16tZAX3Us7T6FI-WbWpDhV1MP2Ygh18k1NrgAYgilxQOXxajr0zdjCrYG4K8Iiqg9DZ6FQyTQcOiN3BvWU213glhoybffV2Rk1aFuD7Kl64nmIjclzVA7ckFDCXaXJb3EdCUDaiTpxW3RvVQ2AeKbyxfkspWIh8lyYn_tY-M1grRYXk6z8mKIsqvE6H1hLqNu8te5GurwaFALjm8B7HTf3Gnl2ocd0fL6n1268memI5MA_1qUkQk7tRRrXtUnlSKg_A8U4G0vfqLkq5VSlMgzHRK6uyK7W6w903iC-RdbMtwzJjElQ0VoYbuctS5q5XKbX4LC9kZiBSkX7uRmz7dF2t5KoqJ1L4m2cKCeYM4FQj4TYR3nOwmvRRAuMKVPSSDZRQfGCVE1CPam9OdWn_L0ufsnOdA2H0UBgZNU7LAjMpHoLoFgJCDzWgE9WUJLK98G758DUUWJaeGjGtetEp5HlLwKx_d22MtJknvsQTnHWLTExQXlZsJOeVHXsdggJwwdVk3Nc7A1K-bIdP_-AhyfWxeu_dnlmtAmQ8PgucM5ubqBAjWSb_UR1itmJXi0KO9y11NSUhwmQTKEFPNajqJLvebWuuuFdTOpkQKSI1oNXtCdKgd-1rEQWMes4E5lhI_386SNEJnsgNcsfld5hAwHXn3hgoQC8qZmABIdXYNJ8Ka-JFGnh_C-95cmW5Zy3BaF-hb02cRqMHD-qddOWZenVoUU64zmeNYGWDqSe1IDtkv7XR-PcSXj4wLGEIjdR7MdG74qna73HyrRXv2TmjP75bPwOj-RNVEskrXfEQZ-lUrBUIqPUpV5r3YeFW1gLZvrnFMY6l1jk3rJPJ9gh-eaO6_00r4n-JmFVgoMt-H31Ip5XIYlCmd4Kx9dndcybsIhOthGz3jI5K3hqVLQxgfMla-dk-3lAViaAMxjLy3ZkuOjcRh81uMGkpaIQU7bKJwZG4Fri2TBF_wrShXIFqRxYSNYvHebI891t5fwVsF1nlTTm2u__xGf3msNaNMbpKa8vc6cj3VUogXI6LPIIKKzdOItl8hDWBhcyW9hdcIdPOUoe62CDne6NvN_aj7OmjFzGiATyOydIVumZE2ZW-vcGxnR_nyE76QUMen703H8CFH4MiczLaaEgcDoWahPWBF0IoDqrz3NVkuNlrPFJIgnrj3vTgfc-q1Kju269YuagBHQRp2f5erCvpKuF7ax8CxEhkWmIzWFwmn5TBiRixxdE2Ayguy_1tBj_QwzvfMLMwq4IAwzDmRB94HO6M2UvySCvhXHEpmKeERnTFO28X_kLK9yqfu6l-G0Ed7rYz0Ukye8v79F3Jo9Y1t1ZrwpraLQqdcHwJMRksHb8ivbUT2Cl6AEZMEIYQ2Wb4OcOpElhBO_mMPnETqbUVJYSSE-F-DnxsjinAiFtiw2n53OxIa_Lf_NPdgBq0fDLi4rrCcDPMkpFsGeohKRZp9p10X8dCWCwxzuzgEAiGTBiLO5UID2B4bnfMb7X5jGihUNuk4iQRP8fgvKY9pUdXLXE0VV9n6iBk43czWIc9xTz73HjkfGF9k5Y5O4PcHBsErozJGvemoPdGi-h2Hc-bJE0OxO5_WZf29MUfMErLnkLnZG-ogrd35YLau4AqGz6QQzdEuyPAC6qMao1xU5i0x3OnrEy-VswxE78Tk_Q_iAk-B8yjzGKwo7Ua9F0rWKoafWDw8DSzd8gEoM6jRfET_h9sIVT2Y5zoqOqACIXl8qWIodF8MYgZhTitinQKP-F2YcFL5Z0uBaor1yVVSvUhQHB0YptZWF5QcPS_satXRgj2tdPJa9UiUdt9ge6XCJXqeMCAkiK-WCtlS5MQ4bRdletSI6ht8ThxnE4DRI67uhvN6dp4nTis_agDQ01T0jMCW9ZTEWPrOboLprIoqByudQjqZuvCqBclx_OPocK6_XgXjkgdQzTuOQJmQK6FL25mxEsIkXbbMTecvpMnXXvFJK7AquE2vz3G3gU9m0JB9rn0JJhLWoa5KeANlJGdJVOQL6OwLx7PeDf-PqnQw3tUyUJKpcauYB1Q_JzazJ4C8xyNAjtxJoWSq3tpCQawhNJTFBHkZW7fTIQGV_0jDfRYr-9QrXI1Ob0V-w9SiRT2kNLIDVJIL0WMXkCefTA4WJqTH9JrxH8yZeJIS18wU28XzOVeydg6PFkjetvqbR4TOay5L-TyxKT_h3K49GBxSZGOaJdw2RAU5l41ZeaBBVu25nP2YafUrYF9DriFUETLepvLu-MqYEqvV3rtrYAdOV3AXBYAMc2AL1rgV87Kb_tt7oSU26zwO7EuCmuPwKkFpt9EEQkdoHAqxs0n5Am5X3_t4ERrGi41xu97X3Z7gnkRaM-TCklvhMEk7h8Q6Qk-8SFbG4rJl60-6ZVI4_AjCCltdGU-_VFNeqcavnNEu4i7qpV2YqHnwayqswJBjIQh8IT2D-1WhchhyE1Q1fMMNzh3OB6z62OyJ1QBQ8fLuwqevRxu-DoF0aLWX-1N5wt2W9_5-ZO_9VgWY5Mx7aYhhJgiNixbbkjnmkyBYuR11VCL9f7J_6Ja0CIVQ83X9_BF-PePHkJzUBtQMF7Wl5yzA74ddDW9W8I-lcvGVtYYZXg2ZdXlN9oo02fwXDCJ-hsnytrAt38lNATPRFt7mA4EhzXfeUiFvsMUZpiQexyFCKuclKAIFWdMK6cMybcbuas807h4E1BG4wXMe_Z0goXt-Z_4zz9nNye407ky16eWhWDAICfIgq5D0SbnKf08KgiK3FgqXcoKbcgYdQkfX4Ov5ALDrr2IgKaugkEn5ZUZYiCMHHLsrXt_stZSYc36CyncmgE34fkdtWxzH4pgyz6hrW-JS82YIDelGassfRIs1rnk8Mj_QXMG9GdP6A7NrlXMTG3D2ainrZk69mH7PMRT8I3UEolkak0rJPHHAgycERQ14iHu_4dJjO_P3Vt_pTlWeRHVUBzd9vKb-mt42SNKsBCTaciTod4TQhet6ZeVECMKCr0o-uljyr7BEaFwRAdHTi8LjbtlncfJHRCLyMC12Fz1GOqAH01yT3NvS__0ncsIYc_VLUf72Ni7PaI3JPhgWt1Aj0SCi955aToK2nHMHZf9VCpUue-KKHUZqu8hSxu5x3e8nchyiEjQR4cxPtXUjTZfJezgw93TaYaElkfcFCI5b5KXA7yc0nYf3L4Cu3aHbnmGUWC_GgtRq3ZTW4lRt26yyZgQHmsCj4a0c_e6aqy7XqBImjS_ruzfMIUQ-kZH-eczH-MDARlO4jrCfdJgjQxxDXOijwESmdbpgw1swmvTeS8N6Y9kZfwJ-FVplu1SxktTgppHGQqMBLwcUaoRNbol2ojB3yxq0mH2QFyUGxU_jRf9Ogayq4XR88yRcVVpFh30en6bg-zDNYIKy44PVnD7q0aayD7ZfDySDdjeYUi4yQE_z2XzgLt2O8haT9bm_v4Ba1L076BPnoX4smbzNJVLMBcN2OPTv7j2p8-IFhmIgBnA7dM4gJql6mdT4Wjvo3SjD4miDYighUPhphj4Y2TTI0NZ7620Cr5iz-tsH8JgOyytiF_OiWREWsj3K1zZO1oEriDRSV1DnvT8CYRZRN2Aq2BVvrKiHYE-HOfm3sekPs71YAbH-BQx5pAIdZkS3Wj1qI1y1jhEISwxPr_h533u15IxZfwJMVEWGMOzLYzzvg_musxUDhB8wa48OuQsV5qhAp56AsMOedm4R0HOka4vjCDAX9VJxXMy9dtU_tmSNp9qLlsDV5Hcv9m2HjUT9gq5bb5HCEtcpPeX2oWJyP-Zol5g_caJ7pUOSTOINY1BlGZnQmId-c5hbx43dH8_Lj-vhTwT9WTBT1ANhA9nQIH_GI8Uxvtl2rt4K-bmfloNKSfEnU5nC73r6KWPvVNx1RETctA92ZAoGOJWuOWzDTMACC8ChROAwRV7BCMIPXt9DURZNUzr7gwROhi6w2xXZrCHQmH3kmANp_qfdQpxP9Jy3itb3nMwmEr_9eeRKBsZWhuG7GdhPx67GZL3D3vD70hC1RDEqMR-pIrZ0Ff2j_bx11w7gNfkU9rQvdwHy6G3AAGfGivS1ptaaoFyTMk-21CWWj7ivluA1g57stP9uBqL2EbGAa8WBNk2HPgYm5Wlup9bwlkHRAV0WZ_46MkCDnL21cIIVIPC0vtPB_CoIPKRzD21C9RmoSzI5DsNTdn9LnxhnaEzm_oYzVZw5lqe3EJb-9RF36YlN6gWlJEvQpB2rIRmvh1TPBbPay_ER1Gz21GAyyNV3I-xNAKBLYpFL_uR0uYC1zR7aUZYznoOr5Msfyg_726UOxSxjhZ9mpjXiLX2ivSmoLq-YHVYgR-Dtua-CvUlOFHRREBICSQwA9CNF4RJ0y8IzmYgFLm31rBhZsimTv-3oBMTNLs5K3KowCqkMMP-rahWrAgJi3A3iLPdQwNzMPC1NUBywKcyJ8hEC7DVTwsVLmT5w_eV0-kyF8Ok8nZlyS237D8uUKnaZxir8MLBqCXRIGu_HHFH39K8-A8rE7QPNG0_lNJpzPqFaVahm8T98HfSwwcFjb5qkwWHBnOmEcBBXl4ZaIux1CgKb3UQ8Fz4FAGNzblE481GHGCkvOdOQTyW2Db5jPO-kYiiVcVel7iId3f1qzZS4IcKet65u3VhM9X0y427jfz0vEGErLGqOmAdtfwEUnUuwpwGT1hZ-2_vdS0hY3NSbrCIwWGulAFDkjIyuaOVvySQ0T4zaDWOtVAEgprBZy9UPFcYLq63QfKdNoxVwa51gJPemqHW_sB9p72mWcwxJXMnYls8MhlLnNTMu7bZDeTHEeZ-dgX25AqlUni3rFZhZW1z2Lv0yThrRmwg45xFbISv63EpF90DonkdezeqDcUmJQG8mR2McqZFS15sLrOICuiNeaKaqTDg6UBSlTzVM0HmVMbxNYUL3ygYzrO8juEkmwGUbkX6cMfXfY4VcDY6x5gMLRqo5GVTKZhPJMYdEiwJWa-lsphb_BuBK8FH4JSAdaae8aLhu1UuVz6AgHAsbQ9_Mb6WCdZMvwHiCtk-QufwogMIA2qyiSt1SOKXrqDk1ugWj7AeGA8Sjg7v-BGUH5udVyC5asgZqXz3qcKzfDQViyb8if2LNM26XEVG9bQYNCRESddomwnp_V-9dKANtScHs3SWkusT1WOpG7gG9Kcpe6s9XjH6C_NP0E-jm4mT5NqrVq5ndZVCuQhuLlnlTv1-D3WN1gtKejqnmFqyHsulQV7_Z8MsMzapC0wkl582QcTJvdP5H52kcnStSZcnVO3Hp0-CEr-b1yFGqfzp0CwvXBue6MMsYvnn9h-xvg4xH8HT5ScaNw2ysqjx7VuSVmRRz7bWs0E79oADV0Mc6hSfuZLJspVnLIeX4Jj9XyUMatMmpq-LWirvZdeSnZyI1yaQjdqQ5lXHVOfoRd53ppYqYXWRUF-FXJsaHGg-AznJmrN_IJR2fF2rBx-dq5o2oJgVNmVr3dvO_Dk8VNNzrANEG3pEKXcvJgiXzU9yMGNEGvucUFUkej2SfmYoV-D2fD30_bfEoXCO8CAiUw6Bi-7g0dR0DvE58bNzyAYPhskJdMALbzUo2AmYoIv2a9gSXD5BEKJ7Vt9i6C01zIDydHxnjBDzqTq20PIo29R_lmgTJgYA9hSvydkZzS1yBJvlHKQ8XkyiR7zlqwMPh6T-aB8gLUZo9EMK9dc4Q.-9GHn5FpV_ZKPuTfxk-ivg">
      <div class="accordion-panel" id="accordion-panel-id">
         <div id="erro-front" class="hidden"></div>
         <div class="container-cadastro">
            <div class="container-formulario">
               <h4>Verificação em duas etapas</h4>
               <p>Clique em <strong>Gerar Código de Acesso</strong> em seu aplicativo gov.br e digite abaixo o código gerado.</p>
               <br>
               <input tabindex="1" onkeypress="submitOnEnterKeyPress('enter-offline-2fa-code')" name="otpInput" id="otpInput" type="number" value="" placeholder="Código de acesso" autocomplete="off" min="0" max="999999">
               <!-- Camada de Sobreposição -->
               <div id="gdd-overlay" class="gdd-overlay gdd-hidden"></div>
               <!-- Hintbox (Balão de Diálogo) -->
               <div id="gdd-hintbox" class="gdd-hintbox gdd-hidden">
                  <button type="button" id="gdd-close-hint" class="gdd-close-btn">X</button>
                  <p>Você pode ativar esta opção caso esteja utilizando um dispositivo confiável. O código não será solicitado nos próximos acessos.</p>
               </div>
               <div class="gdd-checkbox-panel gdd-checkbox-container" style="z-index: 0;">
                  <input class="gdd-checkbox-input-default" type="checkbox" id="device" name="device" value="authorized">
                  <label for="device"> Não solicitar verificação em duas etapas novamente neste navegador</label>
               </div>
               <div style="margin: 1em 0;">
                  <p>Não conseguiu gerar o código?</p>
                  <a id="id-hint-twofactor-a" href="#" onclick="showModal('modal-hint-recover2fa-enabled')">Recuperar acesso à conta gov.br</a>
               </div>
            </div>
         </div>
         <div class="button-panel">
            <button type="submit" name="operation" value="cancel" class="button-cancel" tabindex="3">Cancelar</button>
            <button id="enter-offline-2fa-code" type="submit" name="operation" value="enter-offline-2fa-code" class="button-ok" tabindex="2">Ok</button>
         </div>
      </div>
   </div>
   <div class="modal-hint-recover2fa-enabled">
      <div class="modal-content" id="modal-hint-recover2fa-id">
         <h4 class="colored-link" style="margin-top: 1em;">DIFICULDADES COM A VERIFICAÇÃO EM DUAS ETAPAS?</h4>
         <ul>
            <li>
               <p><strong>Não estou recebendo o código.</strong></p>
               <p>O código não é enviado para seu celular. A única forma de obter o código é logando no aplicativo gov.br e clicando em "Gerar código de acesso".</p>
            </li>
            <li>
               <p><strong>O código de acesso que informei não está funcionando.</strong></p>
               <p>Para que a verificação em duas etapas funcione corretamente é preciso que você ative nas "Configurações" do seu celular a opção de data e hora automáticas.</p>
            </li>
         </ul>
         <hr>
         <h4 class="colored-link" style="margin-top: 1em;">RECUPERAR ACESSO À CONTA GOV.BR</h4>
         <p>Se você está com sua Carteira de Identidade Nacional (CIN) em mãos, <button type="submit" name="operation" value="generate-recover2fa-qrcode" class="button-href-mimic2 colored-link" tabindex="2">recupere o acesso com a CIN</button>.</p>
         <p>Ou entre em contato através do nosso <a class="colored-link" href="https://e.gov.br/2fa" target="_blank">formulário de atendimento</a>.</p>
         <div class="button-panel" id="modal-hint-recover2fa-button-panel">
            <button class="button-cancel" type="button" id="btn-close-modal-hint-recover2fa" onclick="closeModal('modal-hint-recover2fa-enabled')">Cancelar</button>
         </div>
      </div>
   </div>
</form>
```
