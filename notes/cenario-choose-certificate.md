Quando é adicionado um certificado pela primeira vez, é possível que apareça esse popup.

![popup](popup-choose-certificate.png)

Para evitar que esse popup apareça é necessário selecionar ele manualmente, e usar a opção para lembrar para sempre.

## Cenário: Popup de Seleção de Certificado no Firefox

Quando navegando para sites que exigem autenticação por certificado digital, o Firefox pode exibir um popup nativo pedindo para escolher o certificado e clicar em "OK".

### Por que o Selenium NÃO consegue clicar no botão "OK" desse popup?

- Esse popup é gerado pelo navegador/OS, não faz parte do DOM da página.
- Selenium só interage com elementos HTML visíveis no DOM.
- Não existe API Selenium para interagir com popups nativos do browser.

---

## Soluções

### 1. Configurar o Firefox para selecionar o certificado automaticamente

Se você sempre quer usar o mesmo certificado, configure o Firefox para não perguntar:

No seu código (já está assim no seu `main.py`):

```python
options.set_preference("security.default_personal_cert", "Select Automatically")
```

Ou manualmente no Firefox:

- `about:preferences#privacy` → Certificados → Quando um servidor solicitar seu certificado pessoal → "Selecionar um automaticamente"

**Limitação:**

- Se houver mais de um certificado válido, pode não funcionar como esperado (pode escolher o errado ou não escolher nenhum).

---

### 2. Usar automação de sistema operacional (ex: xdotool)

Se não puder evitar o popup, use uma ferramenta externa para simular o clique:

**Exemplo com xdotool (Linux):**

```sh
# Instale o xdotool se não tiver
sudo apt-get install xdotool

# Script para clicar no botão OK do popup
xdotool search --name "certificado.sso.acesso.gov.br" windowactivate --sync key --clearmodifiers Return
```

- Você pode rodar esse comando logo após abrir a página que gera o popup.
- Para automatizar, use `subprocess` no Python:

```python
import subprocess
subprocess.Popen(["xdotool", "search", "--name", "certificado.sso.acesso.gov.br", "windowactivate", "--sync", "key", "--clearmodifiers", "Return"])
```

---

### 3. Instalar o certificado no sistema/Firefox e garantir que só exista UM certificado válido

- Se só houver um certificado, o Firefox seleciona automaticamente.
- Se possível, remova outros certificados do perfil.

---

## Resumo

- Selenium não clica em popups nativos.
- Prefira configurar o Firefox para seleção automática.
- Se não der, use automação de SO (xdotool, AutoIt, etc).
- Certifique-se de que só há um certificado válido para evitar ambiguidade.

---

**Dica extra:**
Se o popup mudar de nome, use `xdotool search --onlyvisible --class "Firefox"` para debugar.

---

Commit: "Explica cenário e soluções para popup de seleção de certificado no Firefox"
