// Script injetado no contexto da página
(function () {
  "use strict";

  // Encobrir a propriedade webdriver antes de qualquer outro script
  Object.defineProperty(navigator, "webdriver", {
    get: function () {
      return false;
    },
    configurable: true,
  });

  // Remover propriedades específicas que podem indicar automação
  delete window.cdc_adoQpoasnfa76pfcZLmcfl_;
  delete window.__webdriver_script_fn;
  delete window.__webdriver_evaluate;
  delete window.__selenium_evaluate;
  delete window.__selenium_unwrapped;
  delete window.__fxdriver_evaluate;
  delete window.__driver_evaluate;
  delete window.$cdc_asdjflasutopfhvcZLmcfl_;
  delete document.$cdc_asdjflasutopfhvcZLmcfl_;

  // Remover atributos específicos do WebDriver
  document.documentElement.removeAttribute("webdriver");
  document.documentElement.removeAttribute("webdriver-evaluate");
  document.documentElement.removeAttribute("selenium");
  document.documentElement.removeAttribute("driver");

  // Interceptar todas as tentativas de detecção baseadas em fingerprinting
  const originalQuery = window.navigator.permissions.query;
  window.navigator.permissions.query = (parameters) =>
    parameters.name === "notifications"
      ? Promise.resolve({ state: Notification.permission })
      : originalQuery(parameters);

  // Falsificar plugins comuns encontrados em navegadores normais
  Object.defineProperty(navigator, "plugins", {
    get: function () {
      // Criar uma estrutura de array similar a plugins
      const fakePlugins = [];

      // Adicionar plugins comuns
      const plugin1 = {
        name: "PDF Viewer",
        filename: "internal-pdf-viewer",
        description: "Portable Document Format",
      };
      const plugin2 = {
        name: "Chrome PDF Viewer",
        filename: "internal-pdf-viewer",
        description: "Portable Document Format",
      };
      const plugin3 = {
        name: "WebEx",
        filename: "npwebex.dll",
        description: "WebEx Content",
      };

      Object.defineProperty(plugin1, "length", { value: 1 });
      Object.defineProperty(plugin2, "length", { value: 1 });
      Object.defineProperty(plugin3, "length", { value: 1 });

      fakePlugins.push(plugin1, plugin2, plugin3);
      Object.defineProperty(fakePlugins, "namedItem", {
        value: function () {
          return null;
        },
      });
      Object.defineProperty(fakePlugins, "refresh", { value: function () {} });

      // Definir comprimento
      Object.defineProperty(fakePlugins, "length", { value: 3 });

      return fakePlugins;
    },
  });

  // Criar uma versão aleatória para simulação
  const getRandomVersion = () => {
    return Math.floor(Math.random() * 89) + 10;
  };

  // Falsificar userAgent com formato mais comum
  Object.defineProperty(navigator, "userAgent", {
    get: function () {
      return (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/" +
        getRandomVersion() +
        ".0.0.0 Safari/537.36"
      );
    },
  });

  // Criar valores aleatórios para propriedades do navegador
  Object.defineProperty(navigator, "hardwareConcurrency", {
    get: function () {
      return 8;
    },
  });

  Object.defineProperty(navigator, "deviceMemory", {
    get: function () {
      return 8;
    },
  });

  // Sobrescrever funções de fingerprinting do canvas
  const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
  CanvasRenderingContext2D.prototype.getImageData = function (x, y, w, h) {
    const imageData = originalGetImageData.apply(this, arguments);
    for (let i = 0; i < imageData.data.length; i += 4) {
      // Alterar valores RGB levemente para evitar fingerprinting
      if (Math.random() < 0.5) {
        imageData.data[i] = Math.max(
          0,
          Math.min(255, imageData.data[i] + (Math.random() < 0.5 ? -1 : 1))
        );
        imageData.data[i + 1] = Math.max(
          0,
          Math.min(255, imageData.data[i + 1] + (Math.random() < 0.5 ? -1 : 1))
        );
        imageData.data[i + 2] = Math.max(
          0,
          Math.min(255, imageData.data[i + 2] + (Math.random() < 0.5 ? -1 : 1))
        );
      }
    }
    return imageData;
  };

  // Sobrescrever toDataURL para evitar fingerprinting
  const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
  HTMLCanvasElement.prototype.toDataURL = function () {
    const result = originalToDataURL.apply(this, arguments);
    if (result.length > 100 && Math.random() < 0.5) {
      // Adicionar ruído sutil à string base64
      const dataStart = result.indexOf(",") + 1;
      const data = result.substring(dataStart);
      const prefix = result.substring(0, dataStart);

      // Alterar alguns caracteres na string base64 para adicionar ruído
      const chars = data.split("");
      for (let i = 0; i < chars.length; i += 100) {
        if (Math.random() < 0.1) {
          chars[i] =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[
              Math.floor(Math.random() * 64)
            ];
        }
      }

      return prefix + chars.join("");
    }
    return result;
  };

  // Registrar mensagem no console
  console.log("[Stealth] Injeção de proteções anti-detecção completa");
})();
