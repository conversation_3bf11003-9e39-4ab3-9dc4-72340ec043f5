// Esconde sinais de automação
Object.defineProperty(navigator, "webdriver", {
  get: () => false,
});
window.navigator.chrome = {
  runtime: {},
};

// Spoof geolocation - Brasília
const mockGeolocation = {
  getCurrentPosition: function (success) {
    success({
      coords: {
        latitude: -15.7801,
        longitude: -47.9292,
        accuracy: 100,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null,
      },
      timestamp: Date.now(),
    });
  },
  watchPosition: function () {
    return Math.floor(Math.random() * 10000);
  },
  clearWatch: function () {},
};

navigator.geolocation = mockGeolocation;

// Esconde canvas fingerprinting
const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
HTMLCanvasElement.prototype.toDataURL = function (type) {
  if (type === "image/png" && this.width === 16 && this.height === 16) {
    // Retorna falso fingerprint para canvas de 16x16
    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
  }
  return originalToDataURL.apply(this, arguments);
};

// Mudança aleatória para hardware concurrency
Object.defineProperty(navigator, "hardwareConcurrency", {
  get: () => 4 + Math.floor(Math.random() * 4),
});

// Esconde detecção de automação via Selenium
// https://intoli.com/blog/not-possible-to-block-chrome-headless/
const originalQuery = window.navigator.permissions.query;
window.navigator.permissions.query = (parameters) =>
  parameters.name === "notifications"
    ? Promise.resolve({ state: Notification.permission })
    : originalQuery(parameters);

// Adiciona funções para prevenir detecção via feature detection
navigator.plugins = [1, 2, 3, 4, 5].map(() => ({
  0: {
    type: "application/x-google-chrome-pdf",
    suffixes: "pdf",
    description: "Portable Document Format",
  },
  name: "PDF Viewer",
  filename: "internal-pdf-viewer",
  description: "Portable Document Format",
  length: 1,
}));

// Simula métodos de mouse não headless
const events = [
  "mousedown",
  "mouseover",
  "mouseenter",
  "mouseout",
  "mouseleave",
];
const mouseEvent = new MouseEvent("mousemove", {
  bubbles: true,
  cancelable: true,
  view: window,
  detail: 0,
  screenX: Math.floor(Math.random() * 800),
  screenY: Math.floor(Math.random() * 600),
  clientX: Math.floor(Math.random() * 800),
  clientY: Math.floor(Math.random() * 600),
});

// Dispatch random mouse events to look human
setTimeout(() => {
  document.dispatchEvent(mouseEvent);
}, Math.floor(Math.random() * 1000));
