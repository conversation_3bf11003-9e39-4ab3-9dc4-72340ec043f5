"""
Este módulo acessa a página do empregador e faz download dos arquivos de empréstimo.
"""

import json
import os
import shutil
import time
from datetime import datetime
from enum import IntEnum
from typing import List, Tuple

from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from goto.utils import EmprestimoDTO, generate_emprestimos_pdf

ELEMENT_APPEAR_WAIT_TIME = int(os.getenv("ELEMENT_APPEAR_WAIT_TIME", "30"))

DOWNLOADS_DIR = "downloads"
SCREENSHOTS_DIR = "screenshots"


class DownloadFailureReason(IntEnum):
    SUCCESS = 0
    INVALID_URL = 1  # A URL não é a esperada.
    NO_RESULTS = 2  # A consulta não retornou resultados.
    DOWNLOAD_FAILED = 3  # O download falhou.
    REDIRECT_TO_LOGIN_PAGE = 4  # A URL mudou para a página de login.
    REDIRECT_TO_COMPANY_PAGE = 5  # A URL mudou para a página de seleção de empresa.
    UNKNOWN = 6  # Outro motivo.


def check_if_file_exists(file_path: str) -> bool:
    """
    Verifica se um arquivo existe no caminho especificado.

    Args:
        file_path: Caminho absoluto do arquivo

    Returns:
        bool: True se o arquivo existe, False caso contrário
    """
    return os.path.isfile(file_path)


def wait_for_download(download_dir: str, expected_filename: str, timeout: int = 30) -> bool:
    """
    Aguarda até que um arquivo seja baixado no diretório especificado.

    Args:
        download_dir: Diretório onde os arquivos são baixados
        timeout: Tempo máximo de espera em segundos
        expected_filename: Nome exato do arquivo esperado (opcional)

    Returns:
        str: Nome do arquivo baixado se encontrado
        None: Se o timeout for atingido ou o arquivo esperado não for encontrado
    """
    start_time = time.time()

    while time.time() - start_time < timeout:
        current_files = set(os.listdir(download_dir))

        if expected_filename in current_files:
            return True
        time.sleep(1)
    return False


def move_file_to_cnpj_folder(file_path: str, cnpj_sem_hifen: str) -> str:
    """
    Move um arquivo para uma pasta específica do CNPJ.

    Args:
        file_path: Caminho do arquivo a ser movido
        cnpj_sem_hifen: CNPJ sem hífen para nomear a pasta

    Returns:
        str: Novo caminho do arquivo
    """
    cnpj_dir = os.path.join(DOWNLOADS_DIR, cnpj_sem_hifen)
    os.makedirs(cnpj_dir, exist_ok=True)

    new_path = os.path.join(cnpj_dir, os.path.basename(file_path))
    shutil.move(file_path, new_path)
    return new_path


def executar_download(browser: WebDriver, cnpj: str, ano: int, mes: int) -> Tuple[bool, int]:
    """
    Resumo de todo o funcionamento:
    - É pressuposto que já esteja selecionado a empresa pelas etapas anteriores. Caso contrário: aborte.
    - Verifica se estamos realmente na página `Arquivos de empréstimos`. Caso contrário: aborte.

    - Os arquivos são salvos na pasta `downloads`.
    - O nome do arquivo é o timestamp da data e hora atual.
    - Existem 3 formatos de arquivos:
        - `json`
        - `csv`
        - `xlsx`
    - Todos os arquivos são salvos na pasta `downloads`.
    - O padrão de nome dos arquivos é, o `mes` é `zero-padded`:
        - `{CNPJ-sem-hífen}-{ano}{mes}.csv`
        - `{CNPJ-sem-hífen}-{ano}{mes}.json`
        - `{CNPJ-sem-hífen}-{ano}{mes}.xlsx`
    - Exemplo:
        - `17784050000100-202505.json`
        - `17784050000100-202505.csv`
        - `17784050000100-202505.xlsx`

    Parametros:
    - ano: int
    - mes: int (1 a 12)

    Retorna:
    - tuple[bool, int]: (True, DownloadFailureReason.SUCCESS) se o download foi realizado com sucesso.
                       (False, reason) caso contrário, onde reason é um DownloadFailureReason.
    """

    # A string deve ser EXATAMENTE como está no html.
    mes_str = {
        1: "Janeiro",
        2: "Fevereiro",
        3: "Março",
        4: "Abril",
        5: "Maio",
        6: "Junho",
        7: "Julho",
        8: "Agosto",
        9: "Setembro",
        10: "Outubro",
        11: "Novembro",
        12: "Dezembro",
    }
    # Plano de ação:
    # 1. Verificação das premissas:
    # - Estamos na página `Arquivos de empréstimos`?
    #   - a url é: https://servicos.mte.gov.br/empregador/#/credito-do-trabalhador/arquivo-emprestimo
    #   - existe o html: <span role="heading" aria-level="1" class="">Arquivos de empréstimos</span>
    #   - existe o html: <input type="text" placeholder="Selecione..." autocomplete="off" id="select-38" aria-label="Ano" value="">
    #   - existe o html: <input type="text" placeholder="Selecione..." autocomplete="off" id="select-55" aria-label="Mês" value="">
    #   - existe o html: <button class="br-button mt-md-4 primary" type="submit" style="width: 100%;"><i class="mt-1 mr-1 fas fa-search" aria-hidden="true"></i>Consultar</button>
    # Cumpridas as premissas, acredita-se que estamos na página `Arquivos de empréstimos`.

    # Cuidados:
    # - Não é possível contar com os IDs dos elementos, alguns são dinâmicos.
    # - É necessário pesquisar pelo elemento usando vários atributos, inclusive o conteúdo do texto.

    # verifica se estamos na página `Arquivos de empréstimos`
    # Navega para a URL: https://servicos.mte.gov.br/empregador/#/credito-do-trabalhador/arquivo-emprestimo
    # Caso a URL mudou, então recebemos um redirect e devemos parar e fazer o login.

    url = "https://servicos.mte.gov.br/empregador/#/credito-do-trabalhador/arquivo-emprestimo"

    print("\nEtapa 3\n")
    print(f"    3.1. Navegando para a URL: {url}")
    browser.get(url)

    time.sleep(1)

    print(f"    3.2. Navegação concluída. Estamos na URL: {browser.current_url}")
    # Verifica se a URL mudou
    if browser.current_url == "https://servicos.mte.gov.br/empregador/#/selecao-empresa-vinculada":
        print(f"    3.3. A URL {browser.current_url} não é a URL esperada. Não é possível continuar.")
        return False, DownloadFailureReason.REDIRECT_TO_COMPANY_PAGE
    elif browser.current_url == "https://servicos.mte.gov.br/empregador/#/login":
        print(f"    3.3. A URL {browser.current_url} não é a URL esperada. Não é possível continuar.")
        return False, DownloadFailureReason.REDIRECT_TO_LOGIN_PAGE
    elif browser.current_url != url:
        print(f"    3.3. A URL {browser.current_url} não é a URL esperada. Não é possível continuar.")
        return False, DownloadFailureReason.INVALID_URL
    else:
        print(f"    3.3. A URL {browser.current_url} é a URL esperada. Continuando...")

    time.sleep(4)

    # Procura pelo elemento `ano`
    # O html do elemento é:
    # - <input type="text" placeholder="Selecione..." autocomplete="off" id="select-1" aria-label="Ano" value="">
    # - <input type="text" placeholder="Selecione..." autocomplete="off" id="select-21" aria-label="Ano" value="">
    print("    3.3. Procurando pelo campo de seleção do ano...")
    ano_xpath = "//input[@aria-label='Ano']"
    ano_input = WebDriverWait(browser, 5).until(EC.element_to_be_clickable((By.XPATH, ano_xpath)))

    # Procura pelo elemento `mês`
    # O html do elemento é:
    # <input type="text" placeholder="Selecione..." autocomplete="off" id="select-38" aria-label="Mês" value="">
    print("    2. Procurando pelo campo de seleção do mês...")
    mes_input_xpath = "//input[@aria-label='Mês']"
    mes_input = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, mes_input_xpath))
    )

    # Procura o elemento `Consultar`
    # O html do elemento é:
    # <button class="br-button mt-md-4 primary" type="submit" style="width: 100%;">
    # <i class="mt-1 mr-1 fas fa-search" aria-hidden="true"></i>Consultar</button>
    print("    3. Procurando botão Consultar...")
    consultar_xpath = "//button[@type='submit'][contains(., 'Consultar')]"
    consultar_btn = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, consultar_xpath))
    )

    # Clica no elemento de input do ano
    # Espera um pouco...
    # Ao clicar irá aparecer um popup com os anos disponíveis.
    # O html do popup é:
    # <label for="202563">2025</label>
    print("    4. Clicando no campo de seleção de ano...")
    ano_input.click()
    time.sleep(1)
    print(f"    4.1. Procurando pelo <label> com o ano: {ano}...")
    # O html do popup é:
    # <label for="202535">2025</label>
    year_label_xpath = f"//label[text()='{ano}']"
    year_label = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, year_label_xpath))
    )
    print(f"    4.2. Clicando na <label> com o ano: {ano}...")
    year_label.click()
    time.sleep(1)  # espera um pouco antes de continuar para o `mês`

    # Clica no elemento de input do mês
    # Espera um pouco...
    # Ao clicar irá aparecer um popup com os meses disponíveis.
    # O html do popup é:
    # <label for="Janeiro113">Janeiro</label>
    # O atributo `for` é aleatório, infelizmente.
    print("    4.3. Procurando opção de mês desejado...")
    mes_input.click()
    time.sleep(1)
    mes_label_xpath = f"//label[text()='{mes_str[mes]}']"
    mes_label = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, mes_label_xpath))
    )
    print(f"    4.4. Clicando no mês desejado: {mes_str[mes]}...")
    mes_label.click()
    time.sleep(1)  # espera um pouco antes de continuar para o `Consultar`

    print("    4.5. Clicando no botão Consultar...")
    consultar_btn = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, consultar_xpath))
    )
    consultar_btn.click()
    time.sleep(1)  # espera um pouco antes de continuar para o `Consultar`

    # A consulta pode retornar ou não resultados.
    # Se não houver resultados, procure pelo seguinte html:
    # <span class="">A consulta não retornou resultados.</span>
    # Mas se houver resultados, procure se o botão de download JSON está visível.
    # Com o seguinte html:
    # <button class="br-button" type="button"><i class="mt-1 mr-1 fas fa-download" aria-hidden="true"></i>JSON</button>
    encontrou = False
    try:
        resultados_xpath = "//span[text()='A consulta não retornou resultados.']"
        WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
            EC.presence_of_element_located((By.XPATH, resultados_xpath))
        )
        print("    4.6. A consulta *não* retornou resultados.")

    except TimeoutException:
        encontrou = True

    if encontrou:
        print("    4.6. A consulta retornou resultados.")

        # Quando há resultados, é esperado que o botão de download JSON seja visível.
        # O html do botão é:
        # <button class="br-button" type="button">
        # <i class="mt-1 mr-1 fas fa-download" aria-hidden="true"></i>JSON</button>
        json_btn_xpath = "//button[@type='button'][contains(., 'JSON')]"
        json_btn = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
            EC.element_to_be_clickable((By.XPATH, json_btn_xpath))
        )
        # Clica no botão de download JSON
        print("    5. Clicando no botão de download JSON...")
        json_btn.click()
        time.sleep(3)  # espera um pouco
        print("    5.1. Aguardando download...")

        cnpj_sem_hifen = cnpj.replace("-", "").replace(".", "").replace("/", "")
        mes_padded = f"{mes:02d}"
        ano_str = f"{ano}"

        expected_filename_json = f"{cnpj_sem_hifen}-{ano_str}{mes_padded}.json"
        result = wait_for_download(DOWNLOADS_DIR, expected_filename_json)
        if result is False:
            raise ValueError("Era esperado um arquivo com o nome: " + expected_filename_json)

        # Move o arquivo JSON para a pasta do CNPJ
        json_path = os.path.join(DOWNLOADS_DIR, expected_filename_json)
        json_path = move_file_to_cnpj_folder(json_path, cnpj_sem_hifen)

        # Clica no botão de download CSV
        # O html do botão é:
        # <button class="br-button" type="button"><i class="mt-1 mr-1 fas fa-download" aria-hidden="true"></i>CSV</button>
        print("    5.2. Clicando no botão de download CSV...")
        csv_btn_xpath = "//button[@type='button'][contains(., 'CSV')]"
        csv_btn = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
            EC.element_to_be_clickable((By.XPATH, csv_btn_xpath))
        )
        csv_btn.click()
        time.sleep(3)  # espera um pouco
        print("    5.3. Aguardando download...")

        expected_filename_csv = f"{cnpj_sem_hifen}-{ano_str}{mes_padded}.csv"
        result = wait_for_download(DOWNLOADS_DIR, expected_filename_csv)
        if result is False:
            raise ValueError("Era esperado um arquivo com o nome: " + expected_filename_csv)

        # Move o arquivo CSV para a pasta do CNPJ
        csv_path = os.path.join(DOWNLOADS_DIR, expected_filename_csv)
        csv_path = move_file_to_cnpj_folder(csv_path, cnpj_sem_hifen)

        # Clica no botão de download XLSX
        # O html do botão é:
        # <button class="br-button" type="button"><i class="mt-1 mr-1 fas fa-download" aria-hidden="true"></i>XLSX</button>
        print("    5.4. Clicando no botão de download XLSX...")
        xlsx_btn_xpath = "//button[@type='button'][contains(., 'XLSX')]"
        xlsx_btn = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
            EC.element_to_be_clickable((By.XPATH, xlsx_btn_xpath))
        )
        xlsx_btn.click()
        time.sleep(3)  # espera um pouco
        print("    5.5. Aguardando download...")

        expected_filename_xlsx = f"{cnpj_sem_hifen}-{ano_str}{mes_padded}.xlsx"
        result = wait_for_download(DOWNLOADS_DIR, expected_filename_xlsx)
        if result is False:
            raise ValueError("Era esperado um arquivo com o nome: " + expected_filename_xlsx)

        # Move o arquivo XLSX para a pasta do CNPJ
        xlsx_path = os.path.join(DOWNLOADS_DIR, expected_filename_xlsx)
        xlsx_path = move_file_to_cnpj_folder(xlsx_path, cnpj_sem_hifen)

        print(f"    6. Os downloads foram salvos na pasta '{DOWNLOADS_DIR}/{cnpj_sem_hifen}'")

        # Tira screenshot da página inteira.
        print("    7. Tira screenshot da página inteira...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_name = f"arquivos-de-emprestimos-{cnpj_sem_hifen}-{ano_str}{mes_padded}-{timestamp}.png"
        screenshot_path = os.path.join(SCREENSHOTS_DIR, screenshot_name)
        browser.save_screenshot(screenshot_path)
        print(f"    7.1. Screenshot capturado em: {screenshot_path}")

        time.sleep(1)

        # Captura um screenshot após o download
        print("    8. Capturando screenshot...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_name = f"arquivos-de-emprestimos-{cnpj_sem_hifen}-{ano_str}{mes_padded}-{timestamp}.png"
        screenshot_path = os.path.join(SCREENSHOTS_DIR, screenshot_name)
        browser.save_screenshot(screenshot_path)
        print(f"    8.1. Screenshot capturado em: {screenshot_path}")

        time.sleep(1)

        # Clica no botão `Detalhar` e tira screenshot do popup.
        # O html do botão é:
        # <button class="br-button circle small" type="button" title="Detalhar" aria-label="botão de Detalhar linha"><i class="fas fa-eye" aria-hidden="true"></i></button>
        detalhar_xpath = "//i[@class='fas fa-eye']"
        detalhar_btn = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
            EC.element_to_be_clickable((By.XPATH, detalhar_xpath))
        )
        detalhar_btn.click()
        time.sleep(1)

        # Gerar um PDF com os dados que estão dentro do arquivo JSON que acabou de ser baixado.
        print("    9. Gerando PDF com os dados...")

        # Parse the JSON file and create EmprestimoDTO instances
        json_path = os.path.join(DOWNLOADS_DIR + "/" + cnpj_sem_hifen, expected_filename_json)

        print(f"    9.1. Processando o arquivo JSON: {json_path}")
        pdf_path = process_json_and_generate_pdf(json_path, cnpj, ano, mes, mes_str[mes])
        move_file_to_cnpj_folder(pdf_path, cnpj_sem_hifen)
        print(f"    9.3. PDF gerado com sucesso: {pdf_path}")

        print("    10. Fim, missão cumprida.")

        return True, DownloadFailureReason.SUCCESS
    return False, DownloadFailureReason.NO_RESULTS


def process_json_and_generate_pdf(json_path: str, cnpj: str, ano: int, mes: int, mes_str: str) -> str:
    """
    Processa o arquivo JSON baixado e gera um PDF com os dados.

    Args:
        json_path: Caminho do arquivo JSON
        cnpj: CNPJ da empresa
        ano: Ano do arquivo
        mes: Mês do arquivo
        mes_str: Nome do mês em português

    Returns:
        str: Caminho do arquivo PDF gerado
    """
    if not os.path.exists(json_path):
        raise FileNotFoundError(f"Arquivo JSON não encontrado: {json_path}")

    if os.path.getsize(json_path) == 0:
        raise ValueError(f"Arquivo JSON está vazio: {json_path}")

    try:
        with open(json_path, "r", encoding="utf-8") as f:
            content = f.read().strip()
            if not content:
                raise ValueError(f"Arquivo JSON está vazio: {json_path}")
            data = json.loads(content)
    except UnicodeDecodeError as exc:
        # Try with latin-1 encoding if utf-8 fails
        with open(json_path, "r", encoding="latin-1") as f:
            content = f.read().strip()
            if not content:
                raise ValueError(f"Arquivo JSON está vazio: {json_path}") from exc
            data = json.loads(content)
    except json.JSONDecodeError as e:
        raise ValueError(f"Erro ao decodificar JSON: {e}") from e

    if not isinstance(data, list):
        raise ValueError(f"JSON inválido: esperava uma lista, recebeu {type(data)}")

    emprestimos: List[EmprestimoDTO] = []
    for item in data:
        emprestimo = EmprestimoDTO(
            if_concessora_codigo=item["ifConcessora.codigo"],
            if_concessora_descricao=item["ifConcessora.descricao"],
            contrato=item["contrato"],
            cpf=item["cpf"],
            matricula=item["matricula"],
            inscricao_empregador_codigo=item["inscricaoEmpregador.codigo"],
            inscricao_empregador_descricao=item["inscricaoEmpregador.descricao"],
            numero_inscricao_empregador=item["numeroInscricaoEmpregador"],
            nome_empregador=item["nomeEmpregador"],
            nome_trabalhador=item["nomeTrabalhador"],
            data_inicio_contrato=item["dataInicioContrato"],
            data_fim_contrato=item["dataFimContrato"] or None,
            competencia_inicio_desconto=item["competenciaInicioDesconto"],
            competencia_fim_desconto=item["competenciaFimDesconto"],
            total_parcelas=item["totalParcelas"],
            valor_parcela=item["valorParcela"],
            valor_emprestimo=item["valorEmprestimo"],
            valor_liberado=item["valorLiberado"],
            qtd_pagamentos=item["qtdPagamentos"],
            qtd_escrituracoes=item["qtdEscrituracoes"],
            categoria_trabalhador_codigo=item["categoriaTrabalhador.codigo"],
            categoria_trabalhador_descricao=item["categoriaTrabalhador.descricao"],
            competencia=item["competencia"],
            inscricao_estabelecimento_codigo=item["inscricaoEstabelecimento.codigo"],
            inscricao_estabelecimento_descricao=item["inscricaoEstabelecimento.descricao"],
            numero_inscricao_estabelecimento=item["numeroInscricaoEstabelecimento"],
        )
        emprestimos.append(emprestimo)

    # Generate PDF with the data
    pdf_path = generate_emprestimos_pdf(
        emprestimos=emprestimos, cnpj=cnpj, ano=ano, mes=mes, mes_str=mes_str, downloads_dir=DOWNLOADS_DIR
    )
    return pdf_path


# Script apenas para testar o funcionamento.
# Se precisar testar, faca:
# python -m goto.download
if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    path = process_json_and_generate_pdf("downloads/17784050000100-202505.json", "17784050000100", 2025, 5, "Maio")
    print(path)
