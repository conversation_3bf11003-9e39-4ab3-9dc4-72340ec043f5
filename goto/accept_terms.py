"""
Esse módulo apenas aceita os termos e condições do portal do empregador num popup que pode ou não aparecer.
"""

import time
from enum import IntEnum
from typing import Tuple

from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from utils import ELEMENT_APPEAR_WAIT_TIME


class SelecionarEmpresaFailureReason(IntEnum):
    SUCCESS = 0
    INVALID_URL = 1  # A URL não é a esperada.
    REDIRECT_TO_LOGIN_PAGE = 2  # A URL mudou para a página de login.
    MODAL_TERMS_AND_CONDITIONS = 3  # Modal de termos e condições.
    UNKNOWN = 4  # Outro motivo.


def executar_aceitar_termos_e_condicoes(browser: WebDriver) -> bool:

    # Verifica se apareceu um modal de termos e condições.
    #   - Se apareceu, clica no botão de aceitar.
    #   - Se não apareceu, continua.
    #   - O html do modal é:
    #    <div class="br-modal _root_slfpf_1 _mobile_slfpf_15 auto" aria-label="Termo de uso Portal Emprega Brasil – Empregador CNPJ: 28.178.298/0001-43"><div class="br-modal-header"><div class="modal-title">Termo de uso Portal Emprega Brasil – Empregador CNPJ: 28.178.298/0001-43</div></div><div class="br-modal-body p-3 p-md-4"><div class="mb-2"><span class="text-up-01 text-bold">A &amp; R CONSTRUCOES LTDA</span></div><span class="text-base"><div class="d-flex flex-column gap-3"><div>Este serviço precisa utilizar as seguintes informações pessoais do cadastro:</div><div><ul><li>Identidade e dados de vinculação da empresa ou do CPF (para pessoas físicas) no gov.br;</li><li>Confiabilidades de sua conta;</li><li>Dados das vagas para captação de trabalhadores;</li><li>Compartilhamento de endereços e telefones para entrevistas e processos seletivos;</li><li>Conversa em chat com o trabalhador.</li></ul></div><div>A partir da sua aprovação, a aplicação mencionada e a plataforma gov.br utilizarão as informações listadas, respeitando os termos de uso e o aviso de privacidade.</div></div></span></div><div class="br-modal-footer pt-0 _footer_slfpf_5"><button class="br-button _footerAction_slfpf_24 secondary" type="button">Trocar empresa</button><button class="br-button _footerAction_slfpf_24 primary" type="button">Aprovar</button></div></div>

    print("4. Executando `executar_aceitar_termos_e_condicoes`")
    print("    4.1. Clicando em aceitar termos")

    # Espera um pouco...
    time.sleep(1)

    div_modal_xpath = "//div[@class='br-modal _root_slfpf_1 _mobile_slfpf_15 auto']"
    WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(EC.element_to_be_clickable((By.XPATH, div_modal_xpath)))

    button_aceitar_xpath = "//button[@class='br-button _footerAction_slfpf_24 primary']"
    button_aceitar = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, button_aceitar_xpath))
    )

    print("    4.2. Clicando no botão em aceitar termos")
    button_aceitar.click()

    # Espera um pouco...
    time.sleep(1)

    return True
