"""O módulo `login` faz com que o driver do navegador acesse o portal do empregador e faça login."""

import time

from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from utils import ELEMENT_APPEAR_WAIT_TIME, NAVIGATE_WAIT_TIME


def executar_login(browser: WebDriver) -> None:
    """
    Acessando o link: https://servicos.mte.gov.br/empregador/#/login
    A rotina abaixo autentica (se necessário) e termina a sua execução
    numa página onde é possível selecionar uma empresa (CNPJ) ou pessoa física (CPF).
    """

    browser.get("https://servicos.mte.gov.br/empregador/#/login")

    # Verifica se estamos na página de login.
    # Para isso, se o seguinte código html existir, então *não* está logado:
    # <span class="d-sm-inline">Entrar</span>

    print("*" * 100)
    print("Etapa 1")
    print("    1. Verificando se estamos na página de login...")
    # Verifica se o botão de logar está presente
    # O html do botão de login é:
    # <span class="d-sm-inline">Entrar</span>
    login_button = WebDriverWait(browser, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[text()='Entrar']"))
    )

    print("    1.1. Botão de login encontrado. Clicando...")
    login_button.click()

    # Terminado a navegação, procure pelo seguinte botão:
    # <button id="login-certificate" type="submit" formaction=
    # "https://certificado.sso.acesso.gov.br/login?client_id=empregador.servicos.mte.gov.br&amp;authorization_id="
    # name="operation" value="login-certificate" class="button-href-mimic2" tabindex="4">
    # <img src="assets/govbr/img/icons/CD.png">
    # Seu certificado digital
    # </button>

    print("    1.2. Aguardando aparecer o botão para logar via certificado...")
    # Clique nesse botão
    login_certificate_button = WebDriverWait(browser, NAVIGATE_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, "//button[@id='login-certificate']"))
    )

    # Espera um pouco assim que carregou a página.
    time.sleep(3)

    print("    1.3. Clicando no botão de certificado digital...")
    login_certificate_button.click()

    # Se o navegador está sob controle remoto (Marionette), pode ser exibido uma mensagem de erro.
    # Mas a solução é simples, é apenas necessário recarregar a página. Apesar do erro exibido, está logado.
    # O seguinte html é exibido:
    # <div class="text-up-01">
    # Ocorreu um erro na tentativa de obter as credenciais de usuário. Por favor, tente novamente mais tarde.
    # </div>
    try:
        WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
            EC.presence_of_element_located(
                (
                    By.XPATH,
                    (
                        "//div[@class='text-up-01' and contains(text(), "
                        "'Ocorreu um erro na tentativa de obter as credenciais de usuário. "
                        "Por favor, tente novamente mais tarde.')]"
                    ),
                )
            )
        )
        # A etapa de login termina por aqui mesmo, a próxima etapa já pode ser executada.
        return
    except TimeoutException:
        pass  # ignore, não apareceu o erro.

    # Caso chegou até aqui, significa que o erro não apareceu. Provavelmente estamos no modo desenvolvedor.

    # Terminado a navegação, verifique se o botão `logout` existe, se existe, estamos autenticados.
    # O html do botão de logout é:
    # <button class="br-button circle small" type="button"
    # style="margin-left: 3px;" aria-label="Abrir Menu de usuário">
    # <i class="fas fa-angle-down" aria-hidden="true"></i></button>
    user_menu_button = WebDriverWait(browser, 10).until(
        EC.element_to_be_clickable((By.XPATH, "//button[@aria-label='Abrir Menu de usuário']"))
    )
    print("    1.4. Clicou no botão do Avatar do usuário")
    user_menu_button.click()
    time.sleep(1)

    # Neste momento um popup deveria ter aparecido, dentro dele está o botão de logout.
    # O html do botão de logout é:
    # <button class="br-button mt-2 secondary" type="button">Sair da conta</button>
    # Se existir, não faça nada, estamos autenticados e esse era o objetivo final.
    # Se não existir, uma exceção será lançada causando um erro fatal.
    WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, "//button[text()='Sair da conta']"))
    )
