"""
O módulo `selecionar empresa` faz com que o driver do navegador acesse o portal do empregador
e selecione a empresa através do CNPJ informado.
"""

import time
from enum import IntEnum
from typing import Tuple

from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from utils import ELEMENT_APPEAR_WAIT_TIME


class SelecionarEmpresaFailureReason(IntEnum):
    SUCCESS = 0
    INVALID_URL = 1  # A URL não é a esperada.
    REDIRECT_TO_LOGIN_PAGE = 2  # A URL mudou para a página de login.
    MODAL_TERMS_AND_CONDITIONS = 3  # Modal de termos e condições.
    UNKNOWN = 4  # Outro motivo.


def executar_selecionar_empresa(browser: WebDriver, cnpj: str) -> <PERSON><PERSON>[bool, SelecionarEmpresaFailureReason]:
    url = "https://servicos.mte.gov.br/empregador/#/selecao-empresa-vinculada"

    print("2. Executando `executar_selecionar_empresa`")
    print(f"    2.1. Navegando para: {url}")
    browser.get(url)

    # Espera um pouco...
    time.sleep(1)

    # Verifica se recebemos um redirect para a página de login.
    if browser.current_url == "https://servicos.mte.gov.br/empregador/#/login":
        print("    2.2. Detectado redirecionamento para a página de login. Não é possível continuar.")
        return False, SelecionarEmpresaFailureReason.REDIRECT_TO_LOGIN_PAGE
    # Verifica se a URL é a esperada.
    elif browser.current_url == url:
        print(f"    2.2. Navegação concluída. Estamos na URL: {browser.current_url}")
    elif browser.current_url == "https://servicos.mte.gov.br/empregador/#/home":
        print("    2.2. Provavelmente apareceu um modal de termos e condições!")
        return False, SelecionarEmpresaFailureReason.MODAL_TERMS_AND_CONDITIONS
    # Url inesperada.
    else:
        print(f"    2.2. Detectado uma URL inesperada: {browser.current_url}. Não é possível continuar.")
        return False, SelecionarEmpresaFailureReason.UNKNOWN

    # html do `div` onde está o `span` do cnpj
    # <span inputmode="numeric">17.784.050/0001-00</span>
    span_cnpj_xpath = f"//span[@inputmode='numeric' and text()='{cnpj}']"
    span_cnpj = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.element_to_be_clickable((By.XPATH, span_cnpj_xpath))
    )
    # Ao clicar é feito um request (AJAX), é necessário esperar o carregamento.
    # A url chamada é:
    # https://mte.api.dataprev.gov.br/apis2/portal-empregador/imo/v1/empregadores/CNPJ/{cnpj}/obterStatusEmpregador
    print("    2.3. Clicando no input cnpj")
    span_cnpj.click()
    time.sleep(5)

    # verifica se o seguinte html existe:
    # <span class="text-up-01 text-medium">Crédito do Trabalhador</span>
    span_xpath = "//span[@class='text-up-01 text-medium' and text()='Crédito do Trabalhador']"
    span = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.presence_of_element_located((By.XPATH, span_xpath))
    )
    print("    2.4. Clicando no <span /> `Crédito do Trabalhador`")
    span.click()
    time.sleep(1)

    # Neste momento é feito uma navegação para a rota: https://servicos.mte.gov.br/empregador/#/credito-do-trabalhador
    # Agora é necessário verificar se existe um botão com o seguinte html:
    # <span class="text-up-01 text-medium">Arquivos de empréstimos</span>
    span_xpath = "//span[@class='text-up-01 text-medium' and text()='Arquivos de empréstimos']"
    span = WebDriverWait(browser, ELEMENT_APPEAR_WAIT_TIME).until(
        EC.presence_of_element_located((By.XPATH, span_xpath))
    )
    print("    2.5. Clicando no <span /> `Arquivos de empréstimos`")
    span.click()
    time.sleep(1)

    if browser.current_url == "https://servicos.mte.gov.br/empregador/#/credito-do-trabalhador/arquivo-emprestimo":
        print(f"    2.6. Navegação concluída. Estamos na URL: {browser.current_url}")
        return True, SelecionarEmpresaFailureReason.SUCCESS
    else:
        print(f"    2.6. Detectado uma URL inesperada: {browser.current_url}. Não é possível continuar.")
        return False, SelecionarEmpresaFailureReason.UNKNOWN
