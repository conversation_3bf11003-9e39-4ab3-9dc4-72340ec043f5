"""
Módulo com funções utilitárias.
"""

import os
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Union

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.platypus import Flowable, PageBreak, Paragraph, SimpleDocTemplate, Spacer, Table, TableStyle


@dataclass
class EmprestimoDTO:
    """DTO para representar um empréstimo consignado."""

    # Instituição Financeira
    if_concessora_codigo: int  # Código da instituição financeira que concedeu o empréstimo
    if_concessora_descricao: str  # Nome da instituição financeira que concedeu o empréstimo

    # Contrato
    contrato: str  # Número do contrato do empréstimo consignado
    cpf: int  # Número do CPF do trabalhador
    matricula: str  # Matrícula atribuída ao trabalhador pelo empregador

    # Empregador
    inscricao_empregador_codigo: int  # Indicador do tipo de inscrição do Empregador
    inscricao_empregador_descricao: str  # Descrição do tipo de inscrição do Empregador
    numero_inscricao_empregador: int  # Identificação principal do empregador no eSocial
    nome_empregador: str  # Nome do empregador

    # Trabalhador
    nome_trabalhador: str  # Nome do trabalhador

    # Datas e Competências
    data_inicio_contrato: str  # Data do início do contrato do empréstimo consignado
    data_fim_contrato: Optional[str]  # Data de fim de contrato do empréstimo consignado
    competencia_inicio_desconto: str  # Competência de início do desconto do empréstimo consignado
    competencia_fim_desconto: str  # Competência de fim do desconto do empréstimo consignado
    competencia: str  # Competência de pesquisa

    # Valores e Quantidades
    total_parcelas: int  # Quantidade total de parcelas do empréstimo do consignado
    valor_parcela: float  # Valor da parcela do empréstimo consignado
    valor_emprestimo: float  # Montante financeiro efetivamente emprestado
    valor_liberado: float  # Valor líquido repassado ao trabalhador após descontos
    qtd_pagamentos: Optional[int]  # Quantidade de pagamentos realizados
    qtd_escrituracoes: Optional[int]  # Quantidade de escriturações realizadas

    # Categoria do Trabalhador
    categoria_trabalhador_codigo: int  # Código da categoria do vínculo do trabalhador
    categoria_trabalhador_descricao: str  # Descrição da categoria do vínculo do trabalhador

    # Estabelecimento
    inscricao_estabelecimento_codigo: int  # Código correspondente ao tipo de inscrição do empregador
    inscricao_estabelecimento_descricao: str  # Descrição correspondente ao tipo de inscrição do empregador
    numero_inscricao_estabelecimento: int  # CNPJ completo (14 dígitos) do estabelecimento


def generate_emprestimos_pdf(
    emprestimos: List[EmprestimoDTO], cnpj: str, ano: int, mes: int, mes_str: str, downloads_dir: str
) -> str:
    """
    Gera um PDF com os dados dos empréstimos, um por página.

    Args:
        emprestimos: Lista de empréstimos
        cnpj: CNPJ da empresa
        ano: Ano da competência
        mes: Mês da competência
        mes_str: Nome do mês
        downloads_dir: Diretório onde o PDF será salvo

    Returns:
        str: Caminho do arquivo PDF gerado
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    cnpj_sem_hifen = cnpj.replace("-", "").replace(".", "").replace("/", "")
    mes_padded = f"{mes:02d}"
    ano_str = str(ano)

    pdf_filename = f"emprestimos-{cnpj_sem_hifen}-{ano_str}{mes_padded}-{timestamp}.pdf"
    pdf_path = os.path.join(downloads_dir, pdf_filename)

    doc = SimpleDocTemplate(pdf_path, pagesize=letter, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=72)
    elements: List[Flowable] = []

    # Estilos
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        "CustomTitle", parent=styles["Heading1"], fontSize=12, spaceAfter=0, alignment=1  # Center alignment
    )

    # Cabeçalho do documento
    elements.append(Paragraph(f"Relatório de Empréstimos - {cnpj_sem_hifen} - {mes_str}/{ano}", title_style))

    # Para cada empréstimo, criar uma nova página
    for i, emp in enumerate(emprestimos):
        if i > 0:  # Adiciona quebra de página para todos exceto o primeiro
            elements.append(PageBreak())

        # Título da página
        elements.append(Paragraph(f"Empréstimo #{i+1}", title_style))
        elements.append(Spacer(1, 20))

        # Dados do empréstimo em formato de tabela
        data: List[List[Union[str, Paragraph]]] = [
            ["Instituição Financeira (Código)", str(emp.if_concessora_codigo)],
            ["Instituição Financeira", emp.if_concessora_descricao],
            ["Contrato", emp.contrato],
            ["CPF", str(emp.cpf)],
            ["Matrícula", emp.matricula],
            ["Empregador (Código Inscrição)", str(emp.inscricao_empregador_codigo)],
            ["Empregador (Tipo Inscrição)", emp.inscricao_empregador_descricao],
            ["Empregador (Número Inscrição)", str(emp.numero_inscricao_empregador)],
            ["Empregador", emp.nome_empregador],
            ["Nome do Trabalhador", emp.nome_trabalhador],
            ["Data Início Contrato", emp.data_inicio_contrato],
            ["Data Fim Contrato", emp.data_fim_contrato or "-"],
            ["Competência Início Desconto", emp.competencia_inicio_desconto],
            ["Competência Fim Desconto", emp.competencia_fim_desconto],
            ["Competência", emp.competencia],
            ["Total de Parcelas", str(emp.total_parcelas)],
            ["Valor da Parcela", f"R$ {emp.valor_parcela:.2f}"],
            ["Valor do Empréstimo", f"R$ {emp.valor_emprestimo:.2f}"],
            ["Valor Liberado", f"R$ {emp.valor_liberado:.2f}"],
            ["Quantidade de Pagamentos", str(emp.qtd_pagamentos or "-")],
            ["Quantidade de Escriturações", str(emp.qtd_escrituracoes or "-")],
            ["Categoria do Trabalhador (Código)", str(emp.categoria_trabalhador_codigo)],
            ["Categoria do Trabalhador", Paragraph(emp.categoria_trabalhador_descricao, styles["Normal"])],
            ["Estabelecimento (Código Inscrição)", str(emp.inscricao_estabelecimento_codigo)],
            ["Estabelecimento (Tipo Inscrição)", emp.inscricao_estabelecimento_descricao],
            ["Estabelecimento (Número Inscrição)", str(emp.numero_inscricao_estabelecimento)],
        ]

        # Find the index of the row to double
        row_heights = [16] * len(data)
        for idx, row in enumerate(data):
            if row[0] == "Categoria do Trabalhador":
                row_heights[idx] = 32  # double height

        # Criar tabela com os dados
        table = Table(data, colWidths=[2.5 * inch, 4 * inch], rowHeights=row_heights)
        table_style = TableStyle(
            [
                ("BACKGROUND", (0, 0), (0, -1), colors.lightgrey),
                ("TEXTCOLOR", (0, 0), (0, -1), colors.black),
                ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                ("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
                ("FONTSIZE", (0, 0), (-1, -1), 10),
                ("BOTTOMPADDING", (0, 0), (-1, -1), 2),
                ("GRID", (0, 0), (-1, -1), 1, colors.black),
                ("ALIGN", (1, 0), (1, -1), "LEFT"),
                ("FONTNAME", (1, 0), (1, -1), "Helvetica"),
            ]
        )
        table.setStyle(table_style)
        elements.append(table)

    # Build PDF
    doc.build(elements)
    return pdf_path
