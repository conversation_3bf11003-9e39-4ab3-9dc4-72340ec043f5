#!/usr/bin/env python3
"""Test script to verify extension creation and validation."""

import os
import zipfile

# Test the extension creation logic
EXTENSIONS_DIR = "extensions"
EXTENSION_PATH = os.path.join(EXTENSIONS_DIR, "stealth_extension")
XPI_PATH = os.path.join(EXTENSIONS_DIR, "stealth_extension.xpi")

def test_extension_creation():
    """Test the extension creation process."""
    print("Testing extension creation...")
    
    # Check if extension files exist
    manifest_path = os.path.join(EXTENSION_PATH, "manifest.json")
    stealth_path = os.path.join(EXTENSION_PATH, "stealth.js")
    
    print(f"Extension directory: {EXTENSION_PATH}")
    print(f"  - manifest.json exists: {os.path.exists(manifest_path)}")
    print(f"  - stealth.js exists: {os.path.exists(stealth_path)}")
    
    if not os.path.exists(manifest_path) or not os.path.exists(stealth_path):
        print("ERROR: Extension source files not found!")
        return False
    
    # Test XPI creation
    test_xpi_path = os.path.join(EXTENSIONS_DIR, "test_stealth_extension.xpi")
    
    try:
        print(f"Creating test XPI: {test_xpi_path}")
        with zipfile.ZipFile(test_xpi_path, "w", zipfile.ZIP_STORED) as xpi:
            xpi.write(manifest_path, "manifest.json")
            xpi.write(stealth_path, "stealth.js")
        
        # Set permissions
        os.chmod(test_xpi_path, 0o644)
        
        # Verify the file
        if os.path.exists(test_xpi_path) and os.path.getsize(test_xpi_path) > 0:
            file_size = os.path.getsize(test_xpi_path)
            print(f"Test XPI created successfully: {file_size} bytes")
            
            # Test file access
            abs_path = os.path.abspath(test_xpi_path)
            print(f"Absolute path: {abs_path}")
            print(f"File readable: {os.access(abs_path, os.R_OK)}")
            
            # Clean up test file
            os.remove(test_xpi_path)
            print("Test XPI cleaned up")
            return True
        else:
            print("ERROR: Failed to create valid test XPI")
            return False
            
    except Exception as e:
        print(f"ERROR creating test XPI: {e}")
        return False

def test_existing_xpi():
    """Test the existing XPI file."""
    print(f"\nTesting existing XPI: {XPI_PATH}")
    
    if not os.path.exists(XPI_PATH):
        print("XPI file does not exist")
        return False
    
    abs_xpi_path = os.path.abspath(XPI_PATH)
    print(f"Absolute path: {abs_xpi_path}")
    
    # Check file properties
    file_size = os.path.getsize(abs_xpi_path)
    print(f"File size: {file_size} bytes")
    
    if file_size == 0:
        print("ERROR: XPI file is empty")
        return False
    
    # Check file permissions
    readable = os.access(abs_xpi_path, os.R_OK)
    print(f"File readable: {readable}")
    
    if not readable:
        print("ERROR: XPI file is not readable")
        return False
    
    # Check ZIP structure
    try:
        with zipfile.ZipFile(abs_xpi_path, 'r') as xpi:
            files = xpi.namelist()
            print(f"Files in XPI: {files}")
            
            if 'manifest.json' not in files or 'stealth.js' not in files:
                print("ERROR: XPI missing required files")
                return False
                
        print("XPI file is valid")
        return True
        
    except Exception as e:
        print(f"ERROR reading XPI: {e}")
        return False

if __name__ == "__main__":
    print("Extension validation test")
    print("=" * 40)
    
    success1 = test_extension_creation()
    success2 = test_existing_xpi()
    
    print("\n" + "=" * 40)
    if success1 and success2:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
